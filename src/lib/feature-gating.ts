/**
 * Feature Gating System
 *
 * Centralized system for managing feature access based on subscription plans
 * and usage limits. Integrates with existing usage API and subscription system.
 */

import { plans } from '@/config/constants'
import { UsageData } from '@/hooks/use-usage'
import { SubscriptionData } from '@/hooks/useSubscription'

// Feature types that can be gated
export type GatedFeature =
  | 'projects'
  | 'videoExports'
  | 'aiImages'
  | 'teamMembers'
  | 'teamInvitations'
  | 'voiceRegenerations'
  | 'videoDuration'
  | 'videoPublishing'
  | 'podcastDuration'

// Feature check result
export interface FeatureCheckResult {
  allowed: boolean
  reason?: string
  current?: number
  limit?: number
  planName?: string
  upgradeRequired?: boolean
}

/**
 * Get plan configuration by name
 * Handles both full plan names (e.g., 'basic-monthly') and base names (e.g., 'basic')
 */
export function getPlanByName(planName: string) {
  const normalizedPlanName = planName.toLowerCase()

  // First, try exact match
  let matchedPlan = plans.find(
    plan => plan.name.toLowerCase() === normalizedPlanName
  )

  // If no exact match, try to match base plan name (e.g., 'basic' matches 'basic-monthly' or 'basic-annual')
  // Note: This defaults to the first match found, which will be the monthly version due to array order
  if (!matchedPlan && !normalizedPlanName.includes('-')) {
    matchedPlan = plans.find(plan =>
      plan.name.toLowerCase().startsWith(normalizedPlanName + '-')
    )
  }

  const result = matchedPlan || plans[0] // Default to free

  return result
}

/**
 * Get current plan from subscription data
 */
export function getCurrentPlan(subscription: SubscriptionData | null) {
  if (!subscription || subscription.status !== 'active') {
    return plans[0] // Free plan
  }

  const matchedPlan = getPlanByName(subscription.plan)

  return matchedPlan
}

/**
 * Check if a feature is allowed based on subscription and usage
 */
export function checkFeatureAccess(
  feature: GatedFeature,
  subscription: SubscriptionData | null,
  usage?: UsageData
): FeatureCheckResult {
  const currentPlan = getCurrentPlan(subscription)

  // Handle special cases first
  switch (feature) {
    case 'teamInvitations':
      if (currentPlan.gatedFeatures?.teamInvitations) {
        return {
          allowed: false,
          reason: 'Team invitations are only available on Premium plan',
          planName: currentPlan.name,
          upgradeRequired: true,
        }
      }
      // For Premium plans, check if they've reached the team member limit
      if (usage && currentPlan.limits.teamMembers > 1) {
        const used = usage.usage.teamMembers?.used || 0
        const teamLimit = currentPlan.limits.teamMembers
        if (used >= teamLimit) {
          return {
            allowed: false,
            reason: `Team member limit reached (${used}/${teamLimit})`,
            current: used,
            limit: teamLimit,
            planName: currentPlan.name,
            upgradeRequired: false, // They're already on premium, just at limit
          }
        }
      }
      return { allowed: true }

    case 'videoPublishing':
      if (currentPlan.gatedFeatures?.videoPublishing) {
        return {
          allowed: false,
          reason: 'Video publishing is only available on Premium plan',
          planName: currentPlan.name,
          upgradeRequired: true,
        }
      }
      return { allowed: true }

    case 'videoExports':
      if (currentPlan.gatedFeatures?.videoExport) {
        return {
          allowed: false,
          reason: 'Video exports are not available on Free plan',
          planName: currentPlan.name,
          upgradeRequired: true,
        }
      }
      // Check usage limits for non-free plans
      if (usage && currentPlan.limits.videoExports > 0) {
        const used = usage.usage.videoExports.used
        const exportLimit = currentPlan.limits.videoExports
        if (used >= exportLimit) {
          return {
            allowed: false,
            reason: `Video export limit reached (${used}/${exportLimit})`,
            current: used,
            limit: exportLimit,
            planName: currentPlan.name,
            upgradeRequired: true,
          }
        }
      }
      return { allowed: true }

    case 'voiceRegenerations':
      // Voice regeneration tracking is now handled via project API in useVoiceRegeneration hook
      // This case should not be used - kept only for type compatibility
      return {
        allowed: false,
        reason:
          'Voice regeneration check should use useVoiceRegeneration hook instead',
      }

    case 'videoDuration':
      // This is handled differently - we return the max allowed duration
      return {
        allowed: true,
        limit: currentPlan.limits.videoDuration,
        planName: currentPlan.name,
      }

    case 'podcastDuration':
      // This is handled differently - we return the max allowed duration
      return {
        allowed: true,
        limit: currentPlan.limits.podcastDuration || 10 * 60, // Default to 10 minutes
        planName: currentPlan.name,
      }

    default:
      // Handle standard usage-based features
      if (!usage) {
        return { allowed: true } // Allow if no usage data available
      }

      const usageKey = feature as keyof typeof usage.usage
      const featureUsage = usage.usage[usageKey]

      if (!featureUsage) {
        return { allowed: true }
      }

      const used = featureUsage.used
      const maxLimit = featureUsage.max

      if (used >= maxLimit) {
        return {
          allowed: false,
          reason: `${feature} limit reached (${used}/${maxLimit})`,
          current: used,
          limit: maxLimit,
          planName: currentPlan.name,
          upgradeRequired: true,
        }
      }

      return { allowed: true, current: used, limit: maxLimit }
  }
}

/**
 * Get user-friendly feature names for UI display
 */
export function getFeatureDisplayName(feature: GatedFeature): string {
  const displayNames: Record<GatedFeature, string> = {
    projects: 'Projects',
    videoExports: 'Video Exports',
    aiImages: 'AI Images',
    teamMembers: 'Team Members',
    teamInvitations: 'Team Invitations',
    voiceRegenerations: 'Voice Regenerations',
    videoDuration: 'Video Duration',
    videoPublishing: 'Video Publishing',
    podcastDuration: 'Podcast Duration',
  }

  return displayNames[feature] || feature
}

/**
 * Get upgrade message for a specific feature
 */
export function getUpgradeMessage(
  feature: GatedFeature,
  planName: string
): string {
  const featureName = getFeatureDisplayName(feature)

  if (planName === 'free') {
    if (feature === 'teamInvitations') {
      return `Upgrade to Premium to invite team members`
    }
    return `Upgrade to Basic or Premium to unlock ${featureName}`
  }

  if (
    planName === 'basic' &&
    (feature === 'videoPublishing' || feature === 'teamInvitations')
  ) {
    return `Upgrade to Premium to unlock ${featureName}`
  }

  return `Upgrade your plan to get more ${featureName}`
}
