'use client'
import { useRef, useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Trash2,
  Play,
  Pause,
  Download,
  Loader2,
  AlertCircle,
  Youtube,
  Gem,
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { YouTubePublishModal } from '@/components/youtube-publish-modal'
import { useVideoPublishingAccess } from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'

interface VideoCardProps {
  thumbnail: string | null
  created_at: Date
  url: string | null
  youtubeId: string | null
  onPublish?: () => void
  status?: 'initializing' | 'rendering' | 'completed' | 'failed'
  progress?: number
  errorMessage?: string
  exportName?: string
  exportResolution?: string
  renderJobId: string
  onDeleted?: () => void
  onYouTubePublished?: (youtubeId: string) => void
}

export function VideoCard({
  thumbnail,
  created_at,
  url,
  youtubeId,
  onPublish,
  status = 'completed',
  progress = 100,
  errorMessage,
  exportName,
  exportResolution,
  renderJobId,
  onDeleted,
  onYouTubePublished,
}: VideoCardProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [playing, setPlaying] = useState(false)
  const [isLoadingDownload, setIsLoadingDownload] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showPublishModal, setShowPublishModal] = useState(false)
  const [currentYouTubeId, setCurrentYouTubeId] = useState(youtubeId)

  const publishingAccess = useVideoPublishingAccess()
  const { openUpgradeModal } = useUpgradeModal()

  // Sync local state with prop changes (important for real-time updates)
  useEffect(() => {
    setCurrentYouTubeId(youtubeId)
  }, [youtubeId])

  const handlePlayPause = () => {
    if (!videoRef.current) return
    if (playing) {
      videoRef.current.pause()
      setPlaying(false)
    } else {
      videoRef.current.play()
      setPlaying(true)
    }
  }

  const handleVideoEnded = () => {
    setPlaying(false)
  }

  const handlePublishClick = () => {
    if (!publishingAccess.allowed) {
      openUpgradeModal('videoPublishing', publishingAccess.upgradeMessage)
      return
    }
    setShowPublishModal(true)
    onPublish?.()
  }

  const handlePublishSuccess = (youtubeId: string) => {
    setCurrentYouTubeId(youtubeId)
    onYouTubePublished?.(youtubeId)
  }

  // Helper function to get signed download URL
  const getDownloadUrl = async () => {
    if (!url) return null

    // Extract path from GCS URL
    // https://storage.googleapis.com/remotioncloudrun-iexx606ijk/renders/userId/renderId.mp4
    // Extract path from AWS URL
    // https://remotionlambda-useast1-klca987fje.s3.us-east-1.amazonaws.com/renders/userId/renderId.mp4

    const filename = exportName
      ? `${exportName}.mp4`
      : `video-${new Date().toISOString().split('T')[0]}.mp4`

    try {
      // Check if it's a GCS URL
      const gcsMatch = url.match(
        /https:\/\/storage\.googleapis\.com\/[^\/]+\/(.+)/
      )
      if (gcsMatch) {
        const filePath = gcsMatch[1]
        const response = await fetch(
          `/api/download-gcp-video/${filePath}?filename=${encodeURIComponent(filename)}`
        )
        if (!response.ok) throw new Error('Failed to get GCS download URL')
        const data = await response.json()
        return data.downloadUrl
      }

      // Check if it's an AWS S3 URL
      const awsMatch = url.match(
        /https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/
      )
      if (awsMatch) {
        const filePath = awsMatch[1]
        const response = await fetch(
          `/api/download-aws-video/${filePath}?filename=${encodeURIComponent(filename)}`
        )
        if (!response.ok) throw new Error('Failed to get AWS download URL')
        const data = await response.json()
        return data.downloadUrl
      }

      // Fallback to original URL
      return url
    } catch (error) {
      console.error('Error getting download URL:', error)
      return url // fallback to original URL
    }
  }

  const handleDownload = async () => {
    if (!url) return

    setIsLoadingDownload(true)
    try {
      const signedUrl = await getDownloadUrl()
      if (signedUrl) {
        const a = document.createElement('a')
        a.href = signedUrl
        a.download = exportName
          ? `${exportName}.mp4`
          : `video-${new Date().toISOString().split('T')[0]}.mp4`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      } else {
        // fallback: open original video link in new tab
        window.open(url, '_blank')
      }
    } catch (error) {
      console.error('Download failed:', error)
      // fallback: open original video link in new tab
      window.open(url, '_blank')
    } finally {
      setIsLoadingDownload(false)
    }
  }

  const handleDelete = async () => {
    if (!renderJobId) return
    setIsDeleting(true)
    // Optimistically remove from UI
    onDeleted?.()
    try {
      // For failed exports, only delete from render job DB since there's no video file
      if (status === 'failed') {
        await fetch(`/api/delete-render-job/${renderJobId}`, {
          method: 'DELETE',
        })
        return
      }

      // For completed videos, delete both video file and render job
      if (!url) {
        throw new Error('No video URL available for deletion')
      }

      // Check if it's a GCS URL
      const gcsMatch = url.match(
        /https:\/\/storage\.googleapis\.com\/[^\/]+\/(.+)/
      )
      if (gcsMatch) {
        const filePath = gcsMatch[1]
        await fetch(`/api/delete-gcs-video/${filePath}?jobId=${renderJobId}`, {
          method: 'DELETE',
        })
        return
      }

      // Check if it's an AWS S3 URL
      const awsMatch = url.match(
        /https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/
      )
      if (awsMatch) {
        const filePath = awsMatch[1]
        await fetch(`/api/delete-aws-video/${filePath}?jobId=${renderJobId}`, {
          method: 'DELETE',
        })
        return
      }

      throw new Error('Invalid video URL - neither GCS nor AWS')
    } catch (err) {
      // Optionally: show a toast or alert
      console.error('Delete failed:', err)
    } finally {
      setIsDeleting(false)
    }
  }

  const getStatusOverlay = () => {
    if (status === 'completed') return null
    return (
      <div className='absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20 flex flex-col items-center justify-center text-white z-20'>
        {status === 'rendering' && (
          <>
            <Loader2 className='w-6 h-6 animate-spin mb-2' />
            <span className='text-sm font-medium mb-2'>Exporting...</span>
            <div className='w-24 bg-white/20 rounded-full h-1.5 backdrop-blur-sm'>
              <div
                className='bg-white h-1.5 rounded-full transition-all duration-500'
                style={{ width: `${progress}%` }}
              />
            </div>
            <span className='text-xs mt-1.5 font-medium'>{progress}%</span>
            <span className='text-xs mt-1 text-white/70'>
              You&apos;ll be notified via email when completed
            </span>
          </>
        )}
        {status === 'initializing' && (
          <>
            <Loader2 className='w-6 h-6 animate-spin mb-2' />
            <span className='text-sm font-medium'>Initializing...</span>
          </>
        )}
        {status === 'failed' && (
          <>
            <AlertCircle className='w-6 h-6 mb-2 text-red-400' />
            <span className='text-sm font-medium'>Export failed</span>
            {errorMessage && (
              <div className='text-xs text-red-200 bg-red-500/20 backdrop-blur-sm p-2 rounded-lg mt-2 max-w-xs text-center border border-red-400/20'>
                {errorMessage}
              </div>
            )}
          </>
        )}
      </div>
    )
  }

  const getTimeText = () => {
    if (status === 'completed') {
      return `Exported ${formatDistanceToNow(created_at, { addSuffix: true })}`
    } else if (status === 'rendering') {
      return `Exporting... ${progress}%`
    } else if (status === 'failed') {
      return 'Export failed'
    } else {
      return 'Initializing...'
    }
  }

  return (
    <Card className='relative group overflow-hidden bg-gradient-to-br from-background !gap-2 to-muted/20 border-2 border-border/50 hover:border-border hover:shadow-lg transition-all duration-300 backdrop-blur-sm p-0'>
      {/* Video preview container - True edge-to-edge design */}
      <div className='relative aspect-video bg-gradient-to-br from-muted/30 to-muted/50 overflow-hidden'>
        {/* Resolution badge - Top left of video area */}
        {exportResolution && (
          <div className='absolute top-3 left-3 z-30'>
            <span className='inline-flex items-center px-2.5 py-1 bg-black/70 backdrop-blur-sm text-white text-xs font-medium rounded-md border border-white/20 shadow-lg'>
              {exportResolution}
            </span>
          </div>
        )}

        {/* Delete button - Top right of video area */}
        {(status === 'completed' || status === 'failed') && (
          <div className='absolute top-3 right-3 z-30'>
            <Button
              size='icon'
              variant='ghost'
              className='w-8 h-8 bg-black/70 backdrop-blur-sm hover:bg-red-600/90 hover:text-white text-white/90 border border-white/20 shadow-lg transition-all duration-300'
              onClick={handleDelete}
              aria-label='Delete video'
              disabled={isDeleting}
            >
              <Trash2 className='w-4 h-4' />
            </Button>
          </div>
        )}

        <video
          ref={videoRef}
          src={url || undefined}
          poster={thumbnail || undefined}
          className='w-full h-full object-contain dark: bg-black'
          onEnded={handleVideoEnded}
        />
        {getStatusOverlay()}

        {/* Play button overlay - only show for completed videos */}
        {status === 'completed' && (
          <div className='absolute inset-0 flex items-center justify-center'>
            <Button
              size='icon'
              variant='secondary'
              className='w-12 h-12 rounded-full bg-background/95 backdrop-blur-sm hover:bg-primary hover:text-primary-foreground border-2 border-white/20 shadow-lg group-hover:scale-105 transition-all duration-300'
              onClick={handlePlayPause}
              aria-label={playing ? 'Pause' : 'Play'}
            >
              {playing ? (
                <Pause className='w-5 h-5' />
              ) : (
                <Play className='w-5 h-5 ml-0.5' />
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Compact card content */}
      <div className='px-4 pb-4 space-y-3'>
        {/* Video info section */}
        <div className='space-y-1.5'>
          <span className='text-xs text-muted-foreground font-medium tracking-wide leading-relaxed'>
            {getTimeText()}
          </span>
          {exportName && (
            <div
              className='text-sm font-medium text-foreground truncate leading-relaxed'
              title={exportName}
            >
              {exportName}
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className='grid grid-cols-1 gap-2.5'>
          {/* Download Video button */}
          <Button
            variant='outline'
            className='w-full'
            onClick={handleDownload}
            disabled={status !== 'completed' || !url || isLoadingDownload}
          >
            {isLoadingDownload ? (
              <Loader2 className='w-4 h-4 mr-2 animate-spin' />
            ) : (
              <Download className='w-4 h-4 mr-2' />
            )}
            {isLoadingDownload ? 'Preparing...' : 'Download Video'}
          </Button>

          {/* YouTube button */}
          {currentYouTubeId ? (
            <Button asChild variant='default' className='w-full'>
              <a
                href={`https://youtube.com/watch?v=${currentYouTubeId}`}
                target='_blank'
                rel='noopener noreferrer'
                className='flex items-center justify-center'
              >
                <Youtube className='w-4 h-4 mr-2' />
                View on YouTube
              </a>
            </Button>
          ) : (
            <Button
              size='sm'
              onClick={handlePublishClick}
              disabled={status !== 'completed' || !url}
              className={`w-full ${
                !publishingAccess.allowed
                  ? 'bg-gradient-to-r from-orange-600 to-orange-500 text-white hover:from-orange-700 hover:to-orange-600'
                  : ''
              }`}
            >
              {!publishingAccess.allowed ? (
                <Gem className='w-4 h-4 mr-2' />
              ) : (
                <Youtube className='w-4 h-4 mr-2' />
              )}
              {!publishingAccess.allowed
                ? 'Upgrade to Publish'
                : 'Publish to YouTube'}
            </Button>
          )}
        </div>
      </div>

      {/* YouTube Publish Modal */}
      <YouTubePublishModal
        isOpen={showPublishModal}
        onClose={() => setShowPublishModal(false)}
        videoUrl={url || ''}
        videoThumbnail={thumbnail}
        videoTitle={exportName || 'Untitled Project'}
        renderJobId={renderJobId}
        onPublishSuccess={handlePublishSuccess}
      />
    </Card>
  )
}
