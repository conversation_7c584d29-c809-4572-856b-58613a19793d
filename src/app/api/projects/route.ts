import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'
import { projects } from '@/db/schema'
import { eq, desc, count } from 'drizzle-orm'
import { onRequestError } from '../../../../instrumentation'
import { getUserSession } from '@/lib/user-utils'
import { z } from 'zod'
import { incrementUsage } from '@/lib/usage-utils'

export async function GET(request: NextRequest) {
  try {
    const session = await getUserSession()

    // const { userId } = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1', 10)
    const limit = parseInt(url.searchParams.get('limit') || '8', 10)
    const offset = (page - 1) * limit

    // Build the where condition based on active organization ID
    let whereCondition
    if (activeOrganizationId) {
      // If user has an active organization, show all projects from that organization
      whereCondition = eq(projects.organizationId, activeOrganizationId)
    } else {
      // If no active organization, show only user's own projects
      whereCondition = eq(projects.userId, userId)
    }

    // Fetch projects with pagination
    const [projectsData, totalCountResult] = await Promise.all([
      db
        .select({
          projectId: projects.projectId,
          projectName: projects.projectName,
          method: projects.method,
          updatedAt: projects.updatedAt,
          createdAt: projects.createdAt,
          coverColor: projects.coverColor,
          coverPic: projects.coverPic,
          orientation: projects.orientation,
          duration: projects.duration,
        })
        .from(projects)
        .where(whereCondition)
        .orderBy(desc(projects.updatedAt))
        .limit(limit)
        .offset(offset),

      db.select({ count: count() }).from(projects).where(whereCondition),
    ])

    const totalCount = totalCountResult[0]?.count || 0
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      projects: projectsData,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    })
  } catch (error) {
    await onRequestError(error, request)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getUserSession()

    // const { userId } = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    const { projectId } = await request.json()

    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      )
    }

    // Check if project exists and user has access to it
    const existingProject = await db
      .select()
      .from(projects)
      .where(eq(projects.projectId, projectId))
      .limit(1)

    if (!existingProject || existingProject.length === 0) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const project = existingProject[0]

    // Check if user has access to this project
    let hasAccess = false

    if (activeOrganizationId) {
      // If user has an active organization, they can delete projects from that organization
      hasAccess = project.organizationId === activeOrganizationId
    } else {
      // If no active organization, they can only delete their own projects
      hasAccess = project.userId === userId
    }

    if (!hasAccess) {
      return NextResponse.json(
        {
          error:
            'Access denied - You do not have permission to delete this project',
        },
        { status: 403 }
      )
    }

    // Delete the project
    await db.delete(projects).where(eq(projects.projectId, projectId))

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Create a new empty project (with one starter scene)
export async function POST(request: NextRequest) {
  try {
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Optional payload to override defaults
    const payloadSchema = z.object({
      projectName: z.string().optional(),
      orientation: z.enum(['landscape', 'portrait', 'square']).optional(),
      text: z.string().optional(),
    })

    let body: z.infer<typeof payloadSchema> = {}
    try {
      if (request.headers.get('content-type')?.includes('application/json')) {
        body = await request.json()
        body = payloadSchema.parse(body)
      }
    } catch {
      // Ignore bad payloads; we'll use defaults
    }

    const sceneText =
      body.text ||
      'Paste your text here and click on select voice button above to generate audio. click on plus icon below to add more scenes to your videos.'

    const now = new Date()

    const starterScene = [
      {
        id: '1',
        title: 'Scene 1',
        text: sceneText,
        duration: 11,
        startOffset: 0,
        captions: [],
        voiceSettings: {
          voiceId: '',
          voiceUrl: '',
          voiceVol: 100,
          voiceName: '',
          voiceSpeed: 1,
        },
        media: {
          url: '',
          thumbnail: '',
          type: 'Image',
          position: { x: 0, y: 0 },
          size: { width: 1920, height: 1080 },
          fit: 'blur',
          kenBurns: 'none',
          transition: { type: 'fade', duration: 1.5 },
        },
      },
    ]

    const [inserted] = await db
      .insert(projects)
      .values({
        userId,
        organizationId: activeOrganizationId || null,
        projectName: body.projectName || 'Untitled Project',
        method: 'Empty Project',
        createdAt: now,
        updatedAt: now,
        coverColor: '#FEE2E2',
        coverPic: null,
        orientation: (body.orientation || 'landscape') as string,
        duration: '11',
        summary: '',
        music: {
          enabled: true,
          src: '',
          volume: 15,
          duration: 0,
          name: 'Background Music',
        },
        captionSettings: {
          enabled: true,
          fontFamily: 'Inter',
          fontSize: 32,
          fontWeight: 'bold',
          fontStyle: 'normal',
          textColor: '#ffffff',
          highlightColor: '#ef4444',
          backgroundColor: '#000000',
          backgroundOpacity: 60,
          textAlign: 'center',
          textShadow: true,
          borderRadius: 8,
          padding: 16,
          maxWidth: 90,
          animation: 'fade',
        },
        scenes: starterScene as unknown as Array<Record<string, unknown>>,
        importedMedia: [],
      })
      .returning({ projectId: projects.projectId })

    // Try to increment usage; ignore failures
    try {
      const referenceId = activeOrganizationId || userId
      if (referenceId) {
        await incrementUsage(referenceId, 'projects', 1)
      }
    } catch {}

    const responseProject = {
      projectId: inserted.projectId,
      userId,
      projectName: body.projectName || 'Untitled Project',
      method: 'Empty Project',
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
      coverColor: '#FEE2E2',
      coverPic: null,
      orientation: (body.orientation || 'landscape') as
        | 'landscape'
        | 'portrait'
        | 'square',
      duration: 11,
      summary: '',
      music: {
        enabled: true,
        src: '',
        volume: 15,
        duration: 0,
        name: 'Background Music',
      },
      speech: null,
      backgroundVideo: null,
      captionSettings: {
        enabled: true,
        fontFamily: 'Inter',
        fontSize: 32,
        fontWeight: 'bold',
        fontStyle: 'normal',
        textColor: '#ffffff',
        highlightColor: '#ef4444',
        backgroundColor: '#000000',
        backgroundOpacity: 60,
        textAlign: 'center',
        textShadow: true,
        borderRadius: 8,
        padding: 16,
        maxWidth: 90,
        animation: 'fade',
      },
      scenes: starterScene,
      importedMedia: [],
      voiceRegenerations: 0,
    }

    return NextResponse.json(responseProject)
  } catch (error) {
    await onRequestError(error, request)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
