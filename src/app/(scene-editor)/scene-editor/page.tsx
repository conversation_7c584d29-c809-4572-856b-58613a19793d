'use client'
import { authClient } from '@/lib/auth-client'
import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import {
  VideoPreviewLayout,
  VideoPlayerContainer,
  VideoEditorHeader,
  ScenesSidebar,
  SceneEditorSkeleton,
  ExportCardSkeleton,
} from '../_components'
import { Modal } from '@/components/ui/modal'
import { VideoCard } from '@/app/(dashboard)/my-videos/_components/VideoCard'

import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { useVideoStore } from '@/store/video-store'
import { useProjectData } from '@/hooks/useProjectData'
import { useProjectSync } from '@/hooks/useProjectSync'
import { useNavigationGuard } from '@/hooks/useNavigationGuard'
import { useCaptionStylesStore } from '@/store'
import { toast } from '@/lib/toast'
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select'
import { useR<PERSON>Jobs } from '@/hooks/useRenderJobs'
import { Loader2, RefreshCw, FileVideo, Calendar, Gem } from 'lucide-react'
import { Scene } from '@/types/video'
import {
  useVideoExportAccess,
  usePlanLimits,
  useVoiceRegeneration,
} from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'
import { PremiumBadge } from '@/components/ui/premium-badge'

export default function SceneEditorPage() {
  const router = useRouter()
  // Mobile toggle state: 'editor' | 'preview'
  const [mobileTab, setMobileTab] = useState<'editor' | 'preview'>('editor')
  const [showRecents, setShowRecents] = useState(false)
  const [showExport, setShowExport] = useState(false)
  const [exportName, setExportName] = useState('My Exported Video')
  const [exportResolution, setExportResolution] = useState('1080p')
  const [isExporting, setIsExporting] = useState(false)
  const [mainLoading, setIsMainLoading] = useState(true)
  const [refreshDelay, setRefreshDelay] = useState(100)

  // Export access control - moved up to be available for quality functions
  const exportAccess = useVideoExportAccess()
  const { openUpgradeModal } = useUpgradeModal()
  const { planName } = usePlanLimits()

  // Quality options based on plan
  const getAvailableQualityOptions = useCallback(() => {
    const allOptions = [
      { value: '1080p', label: '1080p (Full HD)', description: 'Recommended' },
      { value: '720p', label: '720p (HD)', description: 'Smaller file size' },
      { value: '480p', label: '480p (SD)', description: 'Fastest export' },
    ]

    // Premium plan gets all options
    if (planName === 'premium') {
      return allOptions
    }

    // Free and Basic plans only get 720p and below
    return allOptions.filter(option => option.value !== '1080p')
  }, [planName])

  const getDefaultQuality = useCallback(() => {
    return planName === 'premium' ? '1080p' : '720p'
  }, [planName])

  // Set default quality based on plan when plan data loads
  useEffect(() => {
    if (planName && exportResolution === '1080p' && planName !== 'premium') {
      setExportResolution(getDefaultQuality())
    }
  }, [planName, exportResolution, getDefaultQuality])

  const availableQualityOptions = getAvailableQualityOptions()

  const { data: activeOrgData } = authClient.useActiveOrganization()
  const { data: session } = authClient.useSession()

  // Get projectId from URL params
  const searchParams = useSearchParams()
  const projectId = searchParams.get('projectId') ?? null
  const isBlank = searchParams.get('blank') === 'true'

  // If blank=true, create a new empty project then redirect with projectId
  useEffect(() => {
    let didRun = false
    if (isBlank && !projectId && !didRun) {
      didRun = true
      ;(async () => {
        try {
          const res = await fetch('/api/projects', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ projectName: 'Untitled Project' }),
          })
          if (!res.ok) return
          const project = await res.json()
          router.replace(`/scene-editor?projectId=${project.projectId}`)
        } catch {
          // On failure, stay on page; user can navigate back
        }
      })()
    }
  }, [isBlank, projectId, router])

  // Load project data
  const { isLoading, error, projectLoaded } = useProjectData(projectId)
  const {
    scenes,
    project,
    subtitlePosition,
    orientation,
    selectedMusic,
    musicVolume,
    musicEnabled,
  } = useVideoStore()
  const { getEffectiveStyle } = useCaptionStylesStore()

  // Project synchronization
  const { isSaving, lastSaved, hasUnsavedChanges, forceSave } = useProjectSync({
    projectId,
    enabled: !!projectId && projectLoaded,
  })

  // Voice regeneration for refetching after sync
  const voiceRegeneration = useVoiceRegeneration(projectId || '')

  // Enhanced force save that also refetches voice regeneration data
  const enhancedForceSave = useCallback(async () => {
    try {
      await forceSave()
      // Refetch voice regeneration data after successful save
      voiceRegeneration.refetch()
    } catch (error) {
      console.error('Force save failed:', error)
      throw error
    }
  }, [forceSave, voiceRegeneration])

  // Navigation guard to prevent leaving with unsaved changes
  useNavigationGuard({
    hasUnsavedChanges,
    isSaving,
    message:
      'You have unsaved changes in your video project. Are you sure you want to leave without saving?',
  })

  // Update export name when project loads
  useEffect(() => {
    if (project?.projectName) {
      setExportName(project.projectName)
    }
  }, [project?.projectName])

  // Warn about unsaved changes on page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        // Modern browsers ignore the custom message and show their own
        return 'You have unsaved changes. Are you sure you want to leave?'
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // Create a controlled hook call pattern to prevent double API calls
  const [enableRenderJobsHook, setEnableRenderJobsHook] = useState(false)
  const [renderJobsProjectId, setRenderJobsProjectId] = useState<
    string | undefined
  >(undefined)

  console.log('🔍 Scene Editor render jobs state:', {
    showRecents,
    projectId,
    enableRenderJobsHook,
    renderJobsProjectId,
  })

  // Conditionally call the hook - only when enabled, and do NOT fetch all when projectId is missing
  const renderJobsHookResult = useRenderJobs(
    enableRenderJobsHook ? renderJobsProjectId : undefined,
    {
      enabled: enableRenderJobsHook,
      fetchAllWhenNoProject: false,
    }
  )

  // Extract values with fallbacks for when hook is disabled
  const renderJobs = enableRenderJobsHook ? renderJobsHookResult.renderJobs : []
  const isLoadingJobs = enableRenderJobsHook
    ? renderJobsHookResult.isLoading
    : showRecents // Show loading when modal is open but hook disabled
  const refreshJobs = renderJobsHookResult.refreshJobs

  // Check if there are any active jobs (in progress)
  const activeJobs = renderJobs.filter(job =>
    ['initializing', 'rendering', 'exporting'].includes(job.status)
  )

  console.log('📊 Render jobs status:', {
    totalJobs: renderJobs.length,
    activeJobs: activeJobs.length,
    isLoadingJobs,
  })

  useEffect(() => {
    if (showRecents) {
      // Modal opened - enable hook immediately
      setIsMainLoading(true)
      setRenderJobsProjectId(projectId || undefined)
      setEnableRenderJobsHook(true)
      console.log('🟢 Enabling render jobs hook:', { projectId, showRecents })

      // Force a refresh when modal opens to ensure we have the latest data
      setTimeout(() => {
        if (refreshJobs) {
          console.log('🔄 Forcing refresh when modal opens')
          refreshJobs()
          setIsMainLoading(false)
          setRefreshDelay(100)
        }
      }, refreshDelay)
    } else {
      // Modal closed - disable hook after a short delay to allow for any pending updates
      const timer = setTimeout(() => {
        setEnableRenderJobsHook(false)
        setRenderJobsProjectId(undefined)
        console.log('🔴 Disabling render jobs hook - modal closed')
      }, 1000) // 1 second delay to allow for any pending real-time updates

      return () => clearTimeout(timer)
    }
  }, [showRecents, projectId, refreshJobs])

  const totalDuration = scenes.reduce(
    (acc, scene) => acc + (scene.duration || 0),
    0
  )
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Handle video export with access control
  const handleExport = async () => {
    // Check export access first
    if (!exportAccess.allowed) {
      openUpgradeModal('videoExports', exportAccess.upgradeMessage)
      return
    }

    // Filter valid scenes for export
    const validScenes = scenes.filter(
      scene => typeof scene.duration === 'number' && scene.duration > 0
    )

    if (validScenes.length === 0) {
      toast.warning('Please add scenes with voiceover before exporting')
      return
    }

    setIsExporting(true)
    const renderToastId = toast.renderProgress('Preparing video...')

    try {
      // Save project before exporting
      await forceSave()

      // Get the most current subtitle position directly from localStorage
      let currentPosition = { ...subtitlePosition }
      try {
        const savedPosition = localStorage.getItem('subtitlePosition')
        if (savedPosition) {
          currentPosition = JSON.parse(savedPosition)
        }
      } catch (e) {
        console.error('Error getting current subtitle position', e)
      }

      // Calculate composition dimensions based on export resolution and current orientation
      const standardDimensions = {
        landscape: {
          '1080p': { width: 1920, height: 1080 },
          '720p': { width: 1280, height: 720 },
          '480p': { width: 854, height: 480 },
        },
        portrait: {
          '1080p': { width: 1080, height: 1920 },
          '720p': { width: 720, height: 1280 },
          '480p': { width: 480, height: 854 },
        },
        square: {
          '1080p': { width: 1920, height: 1920 },
          '720p': { width: 1280, height: 1280 },
          '480p': { width: 854, height: 854 },
        },
      } as const

      const orientationDimensions =
        standardDimensions[orientation as keyof typeof standardDimensions] ||
        standardDimensions.landscape
      const dimensions =
        orientationDimensions[
          exportResolution as keyof typeof orientationDimensions
        ] || standardDimensions.landscape['1080p']
      const compositionWidth = dimensions.width
      const compositionHeight = dimensions.height

      toast.renderProgress('Submitting export job...', renderToastId)

      // Environment-based rendering logic
      const isLocalDevelopment =
        process.env.NODE_ENV === 'development' ||
        (typeof window !== 'undefined' &&
          window.location.hostname === 'localhost')

      if (isLocalDevelopment) {
        // Use local rendering for development environment

        await localRender(
          validScenes,
          compositionHeight,
          compositionWidth,
          currentPosition,
          renderToastId
        )
      } else {
        // Use Inngest rendering for production environment

        await innjestRender(
          validScenes,
          compositionHeight,
          compositionWidth,
          currentPosition
        )
      }
    } catch (error) {
      console.error('Export error:', error)
      toast.error(
        error instanceof Error
          ? `Export failed: ${error.message}`
          : 'Failed to export video'
      )
    } finally {
      setIsExporting(false)
      toast.dismiss(renderToastId)
    }
  }

  const localRender = async (
    validScenes: Scene[],
    compositionHeight: 1920 | 1080 | 1280 | 720 | 854 | 480,
    compositionWidth: 1920 | 1080 | 1280 | 720 | 854 | 480,
    currentPosition: { x: number; y: number },
    renderToastId: string | number
  ) => {
    // Create the payload that will be sent to remotion-render
    const remotionPayload = {
      scenes: validScenes,
      subtitlePosition: currentPosition,
      orientation: orientation,
      compositionWidth: compositionWidth,
      compositionHeight: compositionHeight,
      selectedMusic: selectedMusic,
      musicVolume: musicVolume,
      musicEnabled: musicEnabled,
      captionStyle: getEffectiveStyle(), // Use current caption style from store
      speech: project?.speech, // Include speech object for audio/podcast workflows
    }

    const res = await fetch('/api/remotion-render', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(remotionPayload),
    })

    if (!res.ok) {
      const errorText = await res.text()
      throw new Error(`Failed to render video: ${errorText}`)
    }

    toast.renderProgress('Download starting...', renderToastId)

    // Create download blob and trigger download
    const blob = await res.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = `${exportName || 'adori-video'}-${exportResolution}.mp4`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    toast.success('Video exported successfully!')
    setShowExport(false)
  }

  const innjestRender = async (
    validScenes: Scene[],
    compositionHeight: 1920 | 1080 | 1280 | 720 | 854 | 480,
    compositionWidth: 1920 | 1080 | 1280 | 720 | 854 | 480,
    currentPosition: { x: number; y: number }
  ) => {
    // Prepare payload for Inngest render function
    const inngestPayload = {
      inputProps: {
        scenes: validScenes,
        subtitlePosition: currentPosition,
        orientation: orientation,
        compositionWidth: compositionWidth,
        compositionHeight: compositionHeight,
        selectedMusic: selectedMusic,
        musicVolume: musicVolume,
        musicEnabled: musicEnabled,
        captionStyle: getEffectiveStyle(),
        exportName,
        exportResolution,
        durationInFrames: Math.round(totalDuration * 30),
        speech: project?.speech,
      },
      projectId,
      userId: project?.userId,
      organizationId: activeOrgData?.id,
      exportResolution,
      duration: totalDuration,
      userEmail: session?.user?.email,
      userName: session?.user?.name,
    }

    // Call API route to trigger Inngest render function
    const res = await fetch('/api/render-video', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(inngestPayload),
    })

    if (!res.ok) {
      const errorText = await res.text()
      throw new Error(`Failed to start export job: ${errorText}`)
    }

    toast.success(
      'Exporting started! You can track progress in Recent Exports.'
    )
    setShowExport(false)
    setRefreshDelay(4000)
    setShowRecents(true)
  }

  // Show loading state while fetching project data
  if ((isBlank && !projectId) || (projectId && isLoading)) {
    return <SceneEditorSkeleton />
  }

  // Show error state if project failed to load
  if (projectId && error) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='flex flex-col items-center gap-4 text-center'>
          <p className='text-red-500'>Failed to load project</p>
          <p className='text-muted-foreground text-sm'>{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    )
  }

  // Show skeleton if projectId exists but project not loaded yet (during initial data fetch)
  if (projectId && !projectLoaded && !isLoading) {
    return <SceneEditorSkeleton />
  }

  return (
    <>
      <VideoPreviewLayout
        headerContent={
          <VideoEditorHeader
            onShowRecents={() => setShowRecents(true)}
            onShowExport={() => setShowExport(true)}
            syncStatus={{
              isSaving,
              lastSaved,
              hasUnsavedChanges,
              forceSave: enhancedForceSave,
            }}
          />
        }
        mobileTab={mobileTab}
        setMobileTab={setMobileTab}
      >
        {/* Left panel: only scenes list/cards, settings are inside each card */}
        <ScenesSidebar projectId={projectId} />
        {/* Right panel: scene preview, needs fullscreen props */}
        {({ isFullscreen, handleFullscreenToggle }) => (
          <VideoPlayerContainer
            isFullscreen={isFullscreen}
            onFullscreenToggle={handleFullscreenToggle}
          />
        )}
      </VideoPreviewLayout>

      {/* Recent Exports Modal */}
      <Modal
        title='Recent Exports'
        description=''
        isOpen={showRecents}
        onClose={() => setShowRecents(false)}
        size='4xl'
      >
        <div className='space-y-4'>
          {/* Header with refresh button */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <div className='p-2 bg-primary/10 rounded-lg'>
                <FileVideo className='h-5 w-5 text-primary' />
              </div>
              <div>
                <p className='text-sm text-muted-foreground'>
                  Track your video exports. You can close this and come back
                  anytime to check progress.
                </p>
              </div>
            </div>
            <Button
              variant='outline'
              size='sm'
              onClick={refreshJobs}
              disabled={isLoadingJobs}
              className='flex items-center gap-2'
            >
              {isLoadingJobs ? (
                <Loader2 className='h-4 w-4 animate-spin' />
              ) : (
                <RefreshCw className='h-4 w-4' />
              )}
              Refresh
            </Button>
          </div>

          {/* Content area */}
          <div className='min-h-[400px]'>
            {isLoadingJobs || mainLoading ? (
              <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
                {Array.from({ length: 6 }).map((_, i) => (
                  <ExportCardSkeleton key={i} />
                ))}
              </div>
            ) : renderJobs.length === 0 ? (
              <div className='flex flex-col items-center justify-center py-12 text-center'>
                <div className='p-4 bg-muted/50 rounded-full mb-4'>
                  <FileVideo className='h-8 w-8 text-muted-foreground' />
                </div>
                <h4 className='text-lg font-medium mb-2'>No exports yet</h4>
                <p className='text-muted-foreground text-sm mb-4 max-w-sm'>
                  Export your first video to see it here. Exported videos will
                  be saved and can be downloaded anytime.
                </p>
                <Button
                  onClick={() => {
                    setShowRecents(false)
                    setShowExport(true)
                  }}
                  className='flex items-center gap-2'
                >
                  <FileVideo className='h-4 w-4' />
                  Export Video
                </Button>
              </div>
            ) : (
              <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
                {renderJobs.map(job => (
                  <VideoCard
                    key={`${job.id}-${job.status}-${job.progress}`}
                    thumbnail={job.thumbnailUrl || null}
                    created_at={job.createdAt}
                    url={job.publicUrl || null}
                    youtubeId={job.youtubeId || null}
                    status={job.status}
                    progress={job.progress}
                    errorMessage={job.errorMessage || undefined}
                    exportName={job.exportName || undefined}
                    exportResolution={job.exportResolution || undefined}
                    renderJobId={job.id}
                    onDeleted={() => {
                      // Refresh the jobs list after deletion
                      refreshJobs()
                    }}
                    onYouTubePublished={() => {
                      // Refresh the jobs list after YouTube publish
                      refreshJobs()
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </Modal>

      {/* Export Video Modal */}
      <Modal
        title='Export Video'
        description='Configure your video export settings'
        isOpen={showExport}
        onClose={() => setShowExport(false)}
        size='md'
        footer={
          <div className='flex items-center justify-between'>
            <div className='text-sm text-muted-foreground'>
              {isSaving ? (
                <span className='flex items-center gap-2'>
                  <Loader2 className='h-3 w-3 animate-spin' />
                  Saving project...
                </span>
              ) : hasUnsavedChanges ? (
                'Project will be saved before export'
              ) : (
                'Project is saved and ready'
              )}
            </div>
            <div className='flex items-center gap-3'>
              <Button
                variant='outline'
                onClick={() => setShowExport(false)}
                disabled={isExporting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleExport}
                disabled={isExporting || !exportName.trim()}
                className={`flex items-center gap-2 ${
                  !exportAccess.allowed
                    ? 'bg-gradient-to-r from-orange-600 to-orange-500 text-white hover:from-orange-700 hover:to-orange-600'
                    : ''
                }`}
              >
                {isExporting ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin' />
                    Exporting...
                  </>
                ) : (
                  <>
                    {!exportAccess.allowed ? (
                      <Gem className='h-4 w-4' />
                    ) : (
                      <FileVideo className='h-4 w-4' />
                    )}
                    {!exportAccess.allowed
                      ? 'Upgrade to Export'
                      : 'Export Video'}
                  </>
                )}
              </Button>
            </div>
          </div>
        }
      >
        <div className='space-y-6'>
          {/* Export Summary Card */}
          <div className='bg-muted/30 rounded-lg p-4 border border-border/50'>
            <div className='flex items-center gap-3 mb-3'>
              <div className='p-2 bg-primary/10 rounded-lg'>
                <FileVideo className='h-5 w-5 text-primary' />
              </div>
              <div className='flex-1'>
                <h4 className='font-medium'>Export Summary</h4>
                <p className='text-sm text-muted-foreground'>
                  Ready to export your video project
                </p>
              </div>
            </div>
            <div className='grid grid-cols-2 gap-4 text-sm'>
              <div className='flex items-center gap-2'>
                <Calendar className='h-4 w-4 text-muted-foreground' />
                <span className='text-muted-foreground'>Duration:</span>
                <span className='font-medium'>
                  {formatDuration(totalDuration)}
                </span>
              </div>
              <div className='flex items-center gap-2'>
                <FileVideo className='h-4 w-4 text-muted-foreground' />
                <span className='text-muted-foreground'>Scenes:</span>
                <span className='font-medium'>{scenes.length}</span>
              </div>
            </div>
          </div>

          {/* Export Settings Form */}
          <div className='space-y-4'>
            <div>
              <Label
                htmlFor='export-name'
                className='text-base font-medium mb-3 block'
              >
                Video Name
              </Label>
              <input
                id='export-name'
                type='text'
                value={exportName}
                onChange={e => setExportName(e.target.value)}
                className='w-full h-10 px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                placeholder='Enter video name...'
              />
              <p className='text-xs text-muted-foreground mt-1'>
                This will be used as the filename for your exported video
              </p>
            </div>

            <div>
              <div className='space-y-2'>
                <Label
                  htmlFor='export-resolution'
                  className='text-base font-medium flex items-center gap-2'
                >
                  Export Quality
                  {planName !== 'premium' && (
                    <PremiumBadge variant='gem' size='sm' />
                  )}
                </Label>
                <Select
                  value={exportResolution}
                  onValueChange={setExportResolution}
                >
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Select resolution' />
                  </SelectTrigger>
                  <SelectContent>
                    {availableQualityOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className='flex items-center gap-2'>
                          <span>{option.label}</span>
                          <span className='text-xs text-muted-foreground'>
                            {option.description}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                    {planName !== 'premium' && (
                      <div className='px-2 py-1.5 text-xs text-muted-foreground border-t'>
                        <div className='flex items-center gap-1'>
                          <Gem className='h-3 w-3 text-orange-500' />
                          <span>1080p available with Premium plan</span>
                        </div>
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}
