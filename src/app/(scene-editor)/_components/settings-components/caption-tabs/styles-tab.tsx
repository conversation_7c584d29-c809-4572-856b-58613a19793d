'use client'

import { useEffect, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Captions, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCaptionStylesStore, useVideoStore } from '@/store'
import { Checkbox } from '@/components/ui/checkbox'
import { CustomTab } from './custom-tab'
import { SubtitleStyle } from '@/types/video'

// Cache for loaded fonts to avoid re-loading
const loadedFontsCache = new Set<string>()

export function StylesTab() {
  const {
    customStyles,
    predefinedStyles,
    selectedStyleId,
    selectStyle,
    removeCustomStyle,
    getAllStyles,
  } = useCaptionStylesStore()

  const { captionsEnabled, setCaptionsEnabled } = useVideoStore()

  const [showCustomSettingsTab, setShowCustomSettingsTab] = useState(false)

  // Create a pseudo-style for "Custom" to be displayed in the list
  const customStyleOption: SubtitleStyle = {
    id: 'custom-preset', // A unique ID for the custom preset button
    name: 'Custom',
    fontFamily: 'Inter',
    fontSize: 50,
    fontWeight: 'normal',
    fontStyle: 'normal',
    textColor: '#ffffff',
    highlightColor: '#3b82f6',
    backgroundColor: '#000000',
    backgroundOpacity: 60,
    animation: 'none',
    textAlign: 'center',
    textShadow: true,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    isCustom: true, // Mark as custom to differentiate from predefined
  }

  // Combine predefined styles with the custom option
  const allPredefinedOptions = [...predefinedStyles, customStyleOption]

  // Load Google Fonts for all styles
  useEffect(() => {
    const allStyles = getAllStyles()
    const uniqueFonts = [...new Set(allStyles.map(style => style.fontFamily))]

    uniqueFonts.forEach(fontFamily => {
      if (loadedFontsCache.has(fontFamily)) return

      // Format font name for Google Fonts URL
      const formattedName = fontFamily.replace(/\s+/g, '+')

      // Check if font is already loaded
      const existingLink = document.querySelector(
        `link[href*="${formattedName}"]`
      )

      if (!existingLink) {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = `https://fonts.googleapis.com/css2?family=${formattedName}:wght@400;700&display=swap`
        document.head.appendChild(link)
        loadedFontsCache.add(fontFamily)
      }
    })
  }, [getAllStyles])

  const handleStyleSelect = (styleId: string) => {
    if (styleId === 'custom-preset') {
      // When custom is selected, immediately activate live preview
      selectStyle('custom-preview')
      setShowCustomSettingsTab(true)
    } else {
      selectStyle(styleId)
      setShowCustomSettingsTab(false)
      // If a predefined style is selected, ensure it's applied
      const style = getAllStyles().find(s => s.id === styleId)
      if (style) {
      }
    }
  }

  const handleRemoveCustomStyle = (
    styleId: string,
    event: React.MouseEvent
  ) => {
    event.stopPropagation() // Prevent style selection when clicking remove
    removeCustomStyle(styleId)
    // If the removed style was the selected one, unselect it or select a default
    if (selectedStyleId === styleId) {
      selectStyle(predefinedStyles[0]?.id || '') // Select first predefined or none
    }
  }

  // Note: getAllStyles() available if needed for future enhancements

  return (
    <div className='h-full flex flex-col'>
      {/* Custom Styles Section */}
      {customStyles.length > 0 && (
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-3'>
            <h4 className='text-xs font-medium text-muted-foreground uppercase tracking-wider'>
              Custom Styles
            </h4>
            <Badge variant='outline' className='text-xs'>
              {customStyles.length}
            </Badge>
          </div>
          <div className='grid grid-cols-1 gap-3'>
            {customStyles.map(style => {
              const isSelected =
                selectedStyleId === style.id ||
                (selectedStyleId === 'custom-preview' &&
                  style.id === 'custom-preset')

              return (
                <div
                  key={style.id}
                  className={cn(
                    'relative rounded-lg border p-4 cursor-pointer transition-all duration-200',
                    isSelected
                      ? 'border-primary ring-2 ring-primary/20 bg-primary/5'
                      : 'border-border hover:border-primary/50 bg-card'
                  )}
                  onClick={() => handleStyleSelect(style.id)}
                >
                  {/* Delete Button */}
                  <div className='absolute bottom-2 right-2'>
                    <Button
                      size='sm'
                      variant='ghost'
                      className='h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/30'
                      onClick={e => handleRemoveCustomStyle(style.id, e)}
                    >
                      <Trash2 className='h-3 w-3' />
                    </Button>
                  </div>

                  {/* Style Preview */}
                  <div className='space-y-3'>
                    {/* Preview Text with Background */}
                    <div className='relative h-16 bg-gray-900 rounded-md overflow-hidden flex items-center justify-center'>
                      <div
                        className='text-center rounded'
                        style={{
                          backgroundColor: `${style.backgroundColor}${Math.round(
                            (style.backgroundOpacity / 100) * 255
                          )
                            .toString(16)
                            .padStart(2, '0')}`,
                          fontFamily: `"${style.fontFamily}", system-ui, sans-serif`,
                          fontSize: `${Math.min(Math.max(style.fontSize * 0.3, 12), 18)}px`, // Better scaling for preview
                          fontWeight: style.fontWeight,
                          fontStyle: style.fontStyle,
                          color: style.textColor,
                          textAlign: style.textAlign,
                          textShadow: style.textShadow
                            ? '0 1px 2px rgba(0,0,0,0.5)'
                            : 'none',
                          borderRadius: `${style.borderRadius}px`,
                          padding: `${Math.min(style.padding * 0.5, 8)}px`,
                          maxWidth: `${style.maxWidth}%`,
                          lineHeight: '1.3',
                        }}
                      >
                        <div style={{ marginBottom: '2px' }}>
                          Let&apos;s{' '}
                          <span
                            style={{
                              backgroundColor: style.highlightColor,
                              color: style.textColor,
                              padding: '1px 4px',
                              borderRadius: '2px',
                            }}
                          >
                            start
                          </span>{' '}
                          with a
                        </div>
                        <div>demo of your caption.</div>
                      </div>
                    </div>

                    {/* Style Info */}
                    <div className='flex items-center justify-between'>
                      <div className='flex-1'>
                        <div className='flex items-center gap-2'>
                          <h4 className='font-medium text-sm'>{style.name}</h4>
                          <Badge
                            variant='secondary'
                            className='text-xs bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                          >
                            Custom
                          </Badge>
                        </div>
                        <div className='flex items-center gap-2 mt-1'>
                          <Badge variant='outline' className='text-xs'>
                            {style.animation === 'none'
                              ? 'No animation'
                              : style.animation}
                          </Badge>
                          {isSelected && (
                            <Badge className='text-xs bg-green-500'>
                              Active
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Color Indicators */}
                      <div className='flex items-center gap-1'>
                        <div
                          className='w-4 h-4 rounded-full border-2 border-white shadow-sm'
                          style={{ backgroundColor: style.textColor }}
                          title='Text Color'
                        />
                        <div
                          className='w-4 h-4 rounded-full border-2 border-white shadow-sm'
                          style={{ backgroundColor: style.highlightColor }}
                          title='Highlight Color'
                        />
                        <div
                          className='w-4 h-4 rounded-full border-2 border-white shadow-sm'
                          style={{
                            backgroundColor: `${style.backgroundColor}${Math.round(
                              (style.backgroundOpacity / 100) * 255
                            )
                              .toString(16)
                              .padStart(2, '0')}`,
                            border:
                              style.backgroundOpacity === 0
                                ? '2px dashed #9ca3af'
                                : '2px solid white',
                          }}
                          title={`Background Color${style.backgroundOpacity === 0 ? ' (Transparent)' : ` (${style.backgroundOpacity}% opacity)`}`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Predefined Styles Section */}
      <div>
        {/* Single Card for Predefined Styles */}
        <div className='relative rounded-lg border p-4 transition-all duration-200 border-border bg-card'>
          <div className='flex items-center justify-between pb-2'>
            <div className='font-medium text-sm flex items-center gap-2'>
              <Captions className='h-4 w-4 ' />
              Captions
            </div>
            <div className='flex items-center gap-2'>
              <Checkbox
                id='disable-audio'
                checked={!captionsEnabled}
                onCheckedChange={checked =>
                  typeof checked === 'boolean' && setCaptionsEnabled(!checked)
                }
              />
              <label
                htmlFor='disable-audio'
                className='text-xs text-muted-foreground select-none'
              >
                Hide captions
              </label>
            </div>
          </div>
          <div className='flex flex-wrap gap-3 justify-center'>
            {allPredefinedOptions.map(style => {
              const isSelected =
                selectedStyleId === style.id ||
                (selectedStyleId === 'custom-preview' &&
                  style.id === 'custom-preset')

              return (
                <button
                  key={style.id}
                  className={cn(
                    'text-center rounded transition-all duration-300 cursor-pointer text-sm font-semibold px-4 py-2',
                    isSelected
                      ? 'ring-2 ring-primary/40 scale-105 shadow-lg'
                      : 'hover:scale-105 hover:shadow-md',
                    'focus:outline-none focus:ring-2 focus:ring-primary/40 focus:ring-offset-2'
                  )}
                  style={{
                    backgroundColor: style.highlightColor,
                    color: style.textColor,
                    borderRadius: '8px',
                  }}
                  onClick={() => handleStyleSelect(style.id)}
                >
                  {style.name}
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {showCustomSettingsTab && (
        <div className='mt-6'>
          <CustomTab
            onStyleCreated={() => {
              // After a custom style is created, switch back to the styles view
              setShowCustomSettingsTab(false)
              selectStyle(customStyles[customStyles.length - 1]?.id || '')
            }}
          />
        </div>
      )}
    </div>
  )
}
