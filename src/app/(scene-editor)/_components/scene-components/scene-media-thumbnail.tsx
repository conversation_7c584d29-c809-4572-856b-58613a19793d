'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { ImageIcon, Eye, Trash2 } from 'lucide-react'
// import { Sparkles } from 'lucide-react'
import Image from 'next/image'
import type { Media } from '@/types/video'

interface SceneMediaThumbnailProps {
  media?: Media
  size?: 'small' | 'medium' | 'large'
  sceneIndex: number
  onChangeMedia: () => void
  onPreviewMedia?: (media: Media) => void
  onRemoveMedia?: () => void
  // showAutoPickHint?: boolean
}

const sizeClasses = {
  small: 'w-12 h-12',
  medium: 'w-16 h-16',
  large: 'w-20 h-20',
}

const containerSizes = {
  small: 'w-14',
  medium: 'w-20',
  large: 'w-20',
}

export function SceneMediaThumbnail({
  media,
  size = 'medium',
  sceneIndex,
  onChangeMedia,
  onPreviewMedia,
  onRemoveMedia,
  // showAutoPickHint = true,
}: SceneMediaThumbnailProps) {
  const hasMedia = !!media?.url

  return (
    <div className={`flex flex-col items-center ${containerSizes[size]}`}>
      <div
        className={`relative ${sizeClasses[size]} rounded-md overflow-hidden bg-muted flex-shrink-0 group border-2 border-transparent hover:border-primary transition flex flex-col items-center cursor-pointer`}
        title='Change media asset'
        onClick={e => {
          e.stopPropagation()
          onChangeMedia()
        }}
      >
        {/* Action buttons */}
        {hasMedia && size !== 'small' && (
          <div className='absolute top-1 left-1 right-1 z-10 flex justify-between opacity-0 group-hover:opacity-100 transition'>
            {onPreviewMedia && (
              <Button
                variant='ghost'
                size='icon'
                className='h-5 w-5 p-0 bg-background/90 hover:bg-blue-100 hover:text-blue-600 border shadow-sm'
                onClick={e => {
                  e.stopPropagation()
                  onPreviewMedia(media!)
                }}
                title='Preview media'
              >
                <Eye className='h-3 w-3' />
              </Button>
            )}
            {onRemoveMedia && (
              <Button
                variant='ghost'
                size='icon'
                className='h-5 w-5 p-0 bg-background/90 hover:bg-red-100 hover:text-red-600 border shadow-sm'
                onClick={e => {
                  e.stopPropagation()
                  onRemoveMedia()
                }}
                title='Remove media'
              >
                <Trash2 className='h-3 w-3' />
              </Button>
            )}
          </div>
        )}

        {/* Media thumbnail or placeholder */}
        {hasMedia && media.thumbnail ? (
          <Image
            src={media.thumbnail}
            alt={`Scene ${sceneIndex + 1}`}
            fill
            className='object-cover group-hover:opacity-80'
            sizes={size === 'small' ? '48px' : '64px'}
          />
        ) : (
          <div className='flex items-center justify-center h-full text-muted-foreground w-full'>
            <ImageIcon className={size === 'small' ? 'h-4 w-4' : 'h-6 w-6'} />
          </div>
        )}

        {/* Change button overlay */}
        {size !== 'small' && (
          <div className='absolute bottom-1 left-1 right-1 flex items-center justify-center opacity-0 group-hover:opacity-100 transition pointer-events-none'>
            <span className='bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded shadow pointer-events-auto'>
              Change
            </span>
          </div>
        )}
      </div>

      {/* Auto-pick hint for empty media */}
      {/* {!hasMedia && showAutoPickHint && (
        <div className='flex items-center gap-1 mt-1 text-xs text-pink-600 font-medium'>
          <Sparkles className={size === 'small' ? 'h-3 w-3' : 'h-4 w-4'} />
          {size === 'small' ? 'Auto' : 'Auto-pick'}
        </div>
      )} */}
    </div>
  )
}
