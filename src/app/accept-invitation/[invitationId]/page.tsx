'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { authClient } from '@/lib/auth-client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Loader2, CheckCircle, XCircle } from 'lucide-react'
import { toast } from 'sonner'

interface AcceptInvitationPageProps {
  params: Promise<{
    invitationId: string
  }>
}

interface InvitationData {
  id: string
  organizationId: string
  email: string
  role: string
  status: string
  expiresAt: Date
  inviterId: string
  createdAt: Date
  organization?: {
    name: string
    slug: string
  }
}

export default function AcceptInvitationPage({
  params,
}: AcceptInvitationPageProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [invitation, setInvitation] = useState<InvitationData | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadInvitation()
  }, [loadInvitation])

  const loadInvitation = useCallback(async () => {
    try {
      const resolvedParams = await params
      const invitationData = await authClient.organization.getInvitation({
        query: {
          id: resolvedParams.invitationId,
        },
      })

      // Check if the response has an error
      if (invitationData.error) {
        console.log('Invitation error:', invitationData.error)
        setError('Invalid or expired invitation link')
        return
      }

      // Extract the data from the response
      const invitation = invitationData.data || invitationData

      // Transform the data to match our interface
      const transformedInvitation: InvitationData = {
        id: invitation.id,
        organizationId: invitation.organizationId,
        email: invitation.email,
        role: invitation.role,
        status: invitation.status,
        expiresAt: new Date(invitation.expiresAt),
        inviterId: invitation.inviterId,
        createdAt: new Date(), // Default value since it's not in the response
        organization: {
          name: invitation.organizationName || 'Unknown Organization',
          slug: invitation.organizationSlug || '',
        },
      }

      setInvitation(transformedInvitation)
    } catch (error) {
      console.log('Error loading invitation:', error)
      setError('Invalid or expired invitation link')
    } finally {
      setLoading(false)
    }
  }, [params])

  const handleAcceptInvitation = async () => {
    try {
      setProcessing(true)
      const resolvedParams = await params
      await authClient.organization.acceptInvitation({
        invitationId: resolvedParams.invitationId,
      })
      toast.success('Invitation accepted successfully!')
      router.push('/')
    } catch {
      toast.error('Failed to accept invitation')
      setError('Failed to accept invitation. Please try again.')
    } finally {
      setProcessing(false)
    }
  }

  const handleRejectInvitation = async () => {
    try {
      setProcessing(true)
      const resolvedParams = await params
      await authClient.organization.rejectInvitation({
        invitationId: resolvedParams.invitationId,
      })
      toast.success('Invitation declined')
      router.push('/')
    } catch {
      toast.error('Failed to decline invitation')
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <Card className='w-full max-w-md'>
          <CardContent className='flex items-center justify-center py-12'>
            <Loader2 className='h-8 w-8 animate-spin' />
            <span className='ml-2'>Loading invitation...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <Card className='w-full max-w-md'>
          <CardContent className='text-center py-12'>
            <XCircle className='h-12 w-12 text-red-500 mx-auto mb-4' />
            <h2 className='text-xl font-semibold mb-2'>Invalid Invitation</h2>
            <p className='text-muted-foreground mb-4'>{error}</p>
            <Button onClick={() => router.push('/')}>Go to Home</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className='min-h-screen flex items-center justify-center p-4'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <CardTitle>Organization Invitation</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='text-center'>
            <CheckCircle className='h-12 w-12 text-green-500 mx-auto mb-4' />
            <h3 className='text-lg font-semibold mb-2'>
              You&apos;ve been invited to join {invitation?.organization?.name}
            </h3>
            <p className='text-muted-foreground mb-4'>
              You&apos;ll be added as a {invitation?.role} to the organization.
            </p>
          </div>

          <div className='space-y-2'>
            <Button
              onClick={handleAcceptInvitation}
              disabled={processing}
              className='w-full bg-primary text-primary-foreground'
            >
              {processing ? (
                <>
                  <Loader2 className='h-4 w-4 animate-spin mr-2' />
                  Accepting...
                </>
              ) : (
                'Accept Invitation'
              )}
            </Button>
            <Button
              onClick={handleRejectInvitation}
              disabled={processing}
              variant='outline'
              className='w-full'
            >
              Decline Invitation
            </Button>
          </div>

          <p className='text-xs text-muted-foreground text-center'>
            By accepting this invitation, you&apos;ll be added to the
            organization and can start collaborating with your team.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
