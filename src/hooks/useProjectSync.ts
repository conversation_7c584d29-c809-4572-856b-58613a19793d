'use client'

import { useEffect, useRef, useCallback, useState } from 'react'
import { useVideoStore } from '@/store/video-store'
import { useCaptionStylesStore } from '@/store'
import { toast } from '@/lib/toast'
import { authClient } from '@/lib/auth-client'
import type { Scene } from '@/types/video'

interface UseProjectSyncOptions {
  projectId: string | null
  enabled?: boolean
}

interface ProjectSyncResult {
  isSaving: boolean
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  saveProject: () => Promise<void>
  forceSave: () => Promise<void>
}

export function useProjectSync({
  projectId,
  enabled = true,
}: UseProjectSyncOptions): ProjectSyncResult {
  const {
    project,
    scenes,
    musicVolume,
    musicEnabled,
    selectedMusic,
    subtitlePosition,
    captionsEnabled,
  } = useVideoStore()

  const {
    selectedStyleId,
    liveCustomStyle,
    isLivePreviewActive,
    getEffectiveStyle,
  } = useCaptionStylesStore()

  // Get active organization for organizationId
  const { data: activeOrgData } = authClient.useActiveOrganization()

  // State for reactive values
  const [isSaving, setIsSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Refs to track state
  const lastSaveRef = useRef<Date | null>(null)
  const lastSavedDataRef = useRef<string | null>(null)

  // Transform store scenes back to API format
  const transformScenesForAPI = useCallback((storeScenes: Scene[]) => {
    return storeScenes.map((scene: Scene) => ({
      captions:
        scene.captions?.map(
          (caption: {
            start: number
            end: number
            text: string
            words: Array<{ start: number; end: number; word: string }>
          }) => ({
            end: caption.end,
            sentence: caption.text,
            start: caption.start,
            wordBoundries:
              caption.words?.map(
                (word: { start: number; end: number; word: string }) => ({
                  end: word.end,
                  start: word.start,
                  word: word.word,
                })
              ) || [],
          })
        ) || [],
      duration: scene.duration !== undefined ? scene.duration : 0,
      id: scene.id,
      media: {
        fit: scene.media?.fit || 'blur',
        kenBurns: scene.media?.kenBurns || 'none',
        position: scene.media?.position || { x: 0, y: 0 },
        thumbnail: scene.media?.thumbnail || '',
        size: scene.media?.size || { height: 1080, width: 1920 },
        transition: {
          duration: scene.media?.effectDuration || 2,
          type: scene.media?.transition || 'fade',
        },
        type: scene.media?.type === 'video' ? 'Video' : 'Image',
        url: scene.media?.url || '',
      },
      startOffset: scene.startOffset !== undefined ? scene.startOffset : 0,
      text: scene.text,
      title: scene.name,
      voiceSettings: {
        voiceId: scene.voiceSettings?.voiceId || '',
        voiceName: scene.voiceSettings?.voiceName || '',
        voiceSpeed: scene.voiceSettings?.voiceSpeed || 1,
        voiceUrl: scene.voiceSettings?.voiceUrl || '',
        voiceVol: scene.voiceSettings?.voiceVol || 100,
      },
    }))
  }, [])

  // Serialize current project data for comparison
  const serializeProjectData = useCallback(() => {
    if (!project) return null

    // Calculate total duration from scenes
    const totalDuration = scenes.reduce(
      (sum, scene) => sum + (scene.duration || 0),
      0
    )

    // Get current effective caption style
    const effectiveStyle = getEffectiveStyle()

    const projectData = {
      projectName: project.projectName,
      orientation: project.orientation,
      coverColor: project.coverColor,
      coverPic: project.coverPic,
      duration: totalDuration,
      summary: project.summary,
      voiceRegenerations: project.voiceRegenerations || 0,
      music: {
        ...project.music,
        enabled: musicEnabled,
        volume: musicVolume,
      },
      speech: project.speech,
      backgroundVideo: project.backgroundVideo,
      // Include updated caption settings
      captionSettings: {
        ...project.captionSettings,
        enabled: captionsEnabled,
        // Include current effective style properties
        ...(effectiveStyle && {
          fontFamily: effectiveStyle.fontFamily,
          fontSize: effectiveStyle.fontSize,
          fontWeight: effectiveStyle.fontWeight,
          fontStyle: effectiveStyle.fontStyle,
          textColor: effectiveStyle.textColor,
          highlightColor: effectiveStyle.highlightColor,
          backgroundColor: effectiveStyle.backgroundColor,
          backgroundOpacity: effectiveStyle.backgroundOpacity,
          animation: effectiveStyle.animation,
          textAlign: effectiveStyle.textAlign,
          textShadow: effectiveStyle.textShadow,
          borderRadius: effectiveStyle.borderRadius,
          padding: effectiveStyle.padding,
          maxWidth: effectiveStyle.maxWidth,
        }),
      },
      scenes: transformScenesForAPI(scenes),
      // Track additional sync data
      _syncData: {
        selectedMusic: selectedMusic,
        subtitlePosition: subtitlePosition,
        selectedStyleId: selectedStyleId,
        liveCustomStyle: liveCustomStyle,
        isLivePreviewActive: isLivePreviewActive,
      },
    }

    return JSON.stringify(projectData)
  }, [
    project,
    scenes,
    musicEnabled,
    musicVolume,
    transformScenesForAPI,
    selectedMusic,
    subtitlePosition,
    captionsEnabled,
    selectedStyleId,
    liveCustomStyle,
    isLivePreviewActive,
    getEffectiveStyle,
  ])

  // Save project to API
  const saveToAPI = useCallback(async (): Promise<boolean> => {
    if (!projectId || !project || isSaving) {
      return false
    }

    try {
      setIsSaving(true)
      setHasUnsavedChanges(false)

      // Calculate total duration from scenes
      const totalDuration = scenes.reduce(
        (sum, scene) => sum + (scene.duration || 0),
        0
      )

      // Get current effective caption style
      const effectiveStyle = getEffectiveStyle()

      const projectData = {
        projectName: project.projectName,
        orientation: project.orientation,
        coverColor: project.coverColor,
        coverPic: project.coverPic,
        duration: totalDuration,
        summary: project.summary,
        music: {
          ...project.music,
          enabled: musicEnabled,
          volume: musicVolume, // Store as percentage (0-100)
          // Include selected music data
          ...(selectedMusic && {
            src: selectedMusic.previewUrl,
            name: selectedMusic.title,
            duration: selectedMusic.durationMillis / 1000,
          }),
        },
        speech: project.speech,
        backgroundVideo: project.backgroundVideo,
        captionSettings: {
          ...project.captionSettings,
          enabled: captionsEnabled,
          // Include current effective style properties
          ...(effectiveStyle && {
            fontFamily: effectiveStyle.fontFamily,
            fontSize: effectiveStyle.fontSize,
            fontWeight: effectiveStyle.fontWeight,
            fontStyle: effectiveStyle.fontStyle,
            textColor: effectiveStyle.textColor,
            highlightColor: effectiveStyle.highlightColor,
            backgroundColor: effectiveStyle.backgroundColor,
            backgroundOpacity: effectiveStyle.backgroundOpacity,
            animation: effectiveStyle.animation,
            textAlign: effectiveStyle.textAlign,
            textShadow: effectiveStyle.textShadow,
            borderRadius: effectiveStyle.borderRadius,
            padding: effectiveStyle.padding,
            maxWidth: effectiveStyle.maxWidth,
          }),
        },
        scenes: transformScenesForAPI(scenes),
        // Include subtitle position data
        subtitlePosition: subtitlePosition,
        voiceRegenerations: project.voiceRegenerations || 0,
      }

      // Include organizationId in the request if available
      const requestData = {
        ...projectData,
        organizationId: activeOrgData?.id || null,
      }

      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save project')
      }

      // Update last saved data and timestamp
      lastSavedDataRef.current = serializeProjectData()
      lastSaveRef.current = new Date()

      return true
    } catch (error) {
      setHasUnsavedChanges(true)
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      console.error('Error saving project:', error)
      toast.error(`Failed to save project: ${errorMessage}`)
      return false
    } finally {
      setIsSaving(false)
    }
  }, [
    projectId,
    project,
    scenes,
    musicEnabled,
    musicVolume,
    selectedMusic,
    subtitlePosition,
    captionsEnabled,
    serializeProjectData,
    transformScenesForAPI,
    isSaving,
    getEffectiveStyle,
    activeOrgData?.id,
  ])

  // Public save function (immediate)
  const saveProject = useCallback(async (): Promise<void> => {
    await saveToAPI()
  }, [saveToAPI])

  // Force save function (same as saveProject but explicit)
  const forceSave = useCallback(async (): Promise<void> => {
    await saveProject()
  }, [saveProject])

  // Monitor for changes and mark as unsaved (no auto-save)
  useEffect(() => {
    if (!enabled || !project) return

    const currentData = serializeProjectData()
    const hasChanged = currentData !== lastSavedDataRef.current

    if (hasChanged && lastSavedDataRef.current !== null) {
      setHasUnsavedChanges(true)
    } else if (lastSavedDataRef.current === null) {
      // First time, store current data without marking as unsaved
      lastSavedDataRef.current = currentData
    }
  }, [
    enabled,
    project,
    scenes,
    musicEnabled,
    musicVolume,
    selectedMusic,
    subtitlePosition,
    captionsEnabled,
    selectedStyleId,
    liveCustomStyle,
    isLivePreviewActive,
    serializeProjectData,
  ])

  // Save on page unload/beforeunload
  useEffect(() => {
    if (!enabled) return

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && !isSaving) {
        // Try to save synchronously using sendBeacon API for reliability
        if (projectId && project) {
          const effectiveStyle = getEffectiveStyle()

          const projectData = {
            projectName: project.projectName,
            orientation: project.orientation,
            coverColor: project.coverColor,
            coverPic: project.coverPic,
            duration: project.duration,
            summary: project.summary,
            music: {
              ...project.music,
              enabled: musicEnabled,
              volume: musicVolume, // Store as percentage (0-100)
              ...(selectedMusic && {
                src: selectedMusic.previewUrl,
                name: selectedMusic.title,
                duration: selectedMusic.durationMillis / 1000,
              }),
            },
            speech: project.speech,
            backgroundVideo: project.backgroundVideo,
            captionSettings: {
              ...project.captionSettings,
              enabled: captionsEnabled,
              ...(effectiveStyle && {
                fontFamily: effectiveStyle.fontFamily,
                fontSize: effectiveStyle.fontSize,
                fontWeight: effectiveStyle.fontWeight,
                fontStyle: effectiveStyle.fontStyle,
                textColor: effectiveStyle.textColor,
                highlightColor: effectiveStyle.highlightColor,
                backgroundColor: effectiveStyle.backgroundColor,
                backgroundOpacity: effectiveStyle.backgroundOpacity,
                animation: effectiveStyle.animation,
                textAlign: effectiveStyle.textAlign,
                textShadow: effectiveStyle.textShadow,
                borderRadius: effectiveStyle.borderRadius,
                padding: effectiveStyle.padding,
                maxWidth: effectiveStyle.maxWidth,
              }),
            },
            scenes: transformScenesForAPI(scenes),
            subtitlePosition: subtitlePosition,
            organizationId: activeOrgData?.id || null,
          }

          // Use sendBeacon for reliability during page unload
          // sendBeacon only supports POST with the data as payload
          const blob = new Blob([JSON.stringify(projectData)], {
            type: 'application/json',
          })
          navigator.sendBeacon(`/api/projects/${projectId}`, blob)
        }

        // Show browser warning
        event.preventDefault()
        event.returnValue =
          'You have unsaved changes. Are you sure you want to leave?'
        return event.returnValue
      }
    }

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && hasUnsavedChanges) {
        // Save when tab becomes hidden
        forceSave()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [
    enabled,
    projectId,
    project,
    scenes,
    musicEnabled,
    musicVolume,
    selectedMusic,
    subtitlePosition,
    captionsEnabled,
    forceSave,
    transformScenesForAPI,
    hasUnsavedChanges,
    isSaving,
    getEffectiveStyle,
    activeOrgData?.id,
  ])

  return {
    isSaving,
    lastSaved: lastSaveRef.current,
    hasUnsavedChanges,
    saveProject,
    forceSave,
  }
}
