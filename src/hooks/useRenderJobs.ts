'use client'

import { useEffect, useState, useCallback } from 'react'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
// import { supabase } from '@/lib/supabase-client'

export interface RenderJob {
  id: string
  projectId: string
  userId: string
  organizationId: string | null
  status: 'initializing' | 'rendering' | 'completed' | 'failed'
  progress: number
  publicUrl: string | null
  thumbnailUrl: string | null
  errorMessage: string | null
  renderMethod: string | null
  exportName: string | null
  exportResolution: string | null
  createdAt: Date
  updatedAt: Date
  youtubeId: string | null
}

// Database row type (snake_case fields from Supabase)
interface RenderJobRow {
  id: string
  project_id: string
  user_id: string
  organization_id: string | null
  status: string
  progress: number | null
  public_url: string | null
  thumbnail_url: string | null
  error_message: string | null
  render_method: string | null
  export_name: string | null
  export_resolution: string | null
  created_at: string | null
  updated_at: string | null
  youtube_id: string | null
}

// Cache interface for localStorage
interface RenderJobsCacheData {
  data: RenderJob[]
  timestamp: number
  projectId?: string
  userId: string
}

// Cache management utility
class RenderJobsCacheManager {
  private static readonly CACHE_KEY = 'adori_render_jobs_cache'
  private static readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  static get(userId: string, projectId?: string): RenderJob[] | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY)
      if (!cached) return null

      const cache: RenderJobsCacheData = JSON.parse(cached)

      // Check if cache is valid
      if (
        cache.userId !== userId ||
        cache.projectId !== projectId ||
        Date.now() - cache.timestamp > this.CACHE_DURATION
      ) {
        this.clear()
        return null
      }

      // Convert date strings back to Date objects
      return cache.data.map(job => ({
        ...job,
        createdAt: new Date(job.createdAt),
        updatedAt: new Date(job.updatedAt),
      }))
    } catch (error) {
      console.error('Error reading render jobs cache:', error)
      this.clear()
      return null
    }
  }

  static set(data: RenderJob[], userId: string, projectId?: string): void {
    try {
      const cache: RenderJobsCacheData = {
        data,
        timestamp: Date.now(),
        projectId,
        userId,
      }
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache))
    } catch (error) {
      console.error('Error writing render jobs cache:', error)
    }
  }

  static clear(): void {
    try {
      localStorage.removeItem(this.CACHE_KEY)
    } catch (error) {
      console.error('Error clearing render jobs cache:', error)
    }
  }

  static invalidate(userId: string, projectId?: string): void {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY)
      if (!cached) return

      const cache: RenderJobsCacheData = JSON.parse(cached)

      // Only clear if the cache matches the current user/project
      if (cache.userId === userId && cache.projectId === projectId) {
        this.clear()
      }
    } catch (error) {
      console.error('Error invalidating render jobs cache:', error)
    }
  }
}

type UseRenderJobsOptions = {
  // If false, the hook will not fetch or subscribe; leaves isLoading=true for skeletons
  enabled?: boolean
  // If false and projectId is undefined, the hook will skip fetching "all" records
  fetchAllWhenNoProject?: boolean
}

export function useRenderJobs(
  projectId?: string,
  options?: UseRenderJobsOptions
) {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [renderJobs, setRenderJobs] = useState<RenderJob[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Normalize options with defaults to simplify type logic
  const isEnabled: boolean = options?.enabled ?? true
  const fetchAllWhenNoProject: boolean = options?.fetchAllWhenNoProject ?? true

  // Normalize database row to RenderJob interface
  const normalizeJob = useCallback(
    (job: RenderJobRow): RenderJob => ({
      id: job.id,
      projectId: job.project_id,
      userId: job.user_id,
      organizationId: job.organization_id,
      status: job.status as
        | 'initializing'
        | 'rendering'
        | 'completed'
        | 'failed',
      progress: job.progress || 0,
      createdAt: job.created_at ? new Date(job.created_at) : new Date(),
      updatedAt: job.updated_at ? new Date(job.updated_at) : new Date(),
      thumbnailUrl: job.thumbnail_url,
      publicUrl: job.public_url,
      youtubeId: job.youtube_id,
      errorMessage: job.error_message,
      renderMethod: job.render_method,
      exportName: job.export_name,
      exportResolution: job.export_resolution,
    }),
    []
  )

  // Fetch render jobs with caching
  const fetchRenderJobs = useCallback(
    async (useCache = true) => {
      if (!session?.user?.id) {
        return
      }

      // Early return if we shouldn't fetch
      if (!isEnabled) {
        return
      }

      if (!projectId && !fetchAllWhenNoProject) {
        return
      }

      // Try to get from cache first
      if (useCache) {
        const cachedJobs = RenderJobsCacheManager.get(
          session.user.id,
          projectId
        )
        if (cachedJobs) {
          setRenderJobs(cachedJobs)
          setIsLoading(false)
          return
        }
      }

      try {
        // Use API route instead of direct Supabase call
        const params = new URLSearchParams()
        if (projectId) {
          params.append('projectId', projectId)
        }

        const apiUrl = `/api/render-jobs${params.toString() ? `?${params.toString()}` : ''}`

        const response = await fetch(apiUrl)

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          console.error('❌ API response error:', {
            status: response.status,
            statusText: response.statusText,
            error: errorData,
          })
          throw new Error(
            `HTTP error! status: ${response.status}, message: ${errorData.error || 'Unknown error'}`
          )
        }

        const { data } = await response.json()

        // Normalize DB fields to camelCase for UI (same as before)
        const normalized = ((data as RenderJobRow[]) || []).map(normalizeJob)

        setRenderJobs(normalized)

        // Cache the results
        RenderJobsCacheManager.set(normalized, session.user.id, projectId)
      } catch (error) {
        console.error('❌ Error fetching render jobs:', error)
      } finally {
        setIsLoading(false)
      }
    },
    [
      session?.user?.id,
      projectId,
      isEnabled,
      fetchAllWhenNoProject,
      normalizeJob,
    ]
  )

  // Initial data fetch
  useEffect(() => {
    if (!session?.user?.id) {
      return
    }

    // If disabled, keep loading state true for skeletons and do not fetch/subscribe
    if (!isEnabled) {
      setIsLoading(true)
      setRenderJobs([])
      return
    }

    // If caller only wants project-specific fetches but projectId is missing, keep loading and wait
    if (!projectId && !fetchAllWhenNoProject) {
      setIsLoading(true)
      setRenderJobs([])
      return
    }

    // Fetch initial data immediately
    fetchRenderJobs(true)

    return () => {
      console.log('🧹 Cleaning up useRenderJobs effect')
    }
  }, [
    session?.user?.id,
    session?.session?.activeOrganizationId,
    projectId,
    isEnabled,
    fetchAllWhenNoProject,
    fetchRenderJobs,
  ])

  // Separate effect to manage polling based on job status changes
  useEffect(() => {
    if (!isEnabled || !session?.user?.id) return

    // Check if there are any active jobs
    const hasActiveJobs = renderJobs.some(job =>
      ['initializing', 'rendering', 'exporting'].includes(job.status)
    )

    if (hasActiveJobs) {
      const pollInterval = setInterval(() => {
        fetchRenderJobs(false) // Force fresh fetch
      }, 3000) // Poll every 3 seconds

      return () => {
        clearInterval(pollInterval)
        console.log('🔄 Stopped polling')
      }
    }
  }, [renderJobs, isEnabled, session?.user?.id, fetchRenderJobs])

  // Manual refresh function that bypasses cache
  const refreshJobs = useCallback(async () => {
    if (!session?.user?.id) return
    setIsLoading(true)
    await fetchRenderJobs(false) // Force fresh fetch
  }, [session?.user?.id, fetchRenderJobs])

  return {
    renderJobs,
    isLoading,
    refreshJobs,
  }
}

// Paginated render jobs for /my-videos with real-time updates
export function usePaginatedRenderJobs(page = 1, limit = 8) {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [jobs, setJobs] = useState<RenderJob[]>([])
  const [total, setTotal] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  // Normalize database row to RenderJob interface
  const normalizeJob = useCallback(
    (job: RenderJobRow): RenderJob => ({
      id: job.id,
      projectId: job.project_id,
      userId: job.user_id,
      organizationId: job.organization_id,
      status: job.status as
        | 'initializing'
        | 'rendering'
        | 'completed'
        | 'failed',
      progress: job.progress || 0,
      createdAt: job.created_at ? new Date(job.created_at) : new Date(),
      updatedAt: job.updated_at ? new Date(job.updated_at) : new Date(),
      thumbnailUrl: job.thumbnail_url,
      publicUrl: job.public_url,
      youtubeId: job.youtube_id,
      errorMessage: job.error_message,
      renderMethod: job.render_method,
      exportName: job.export_name,
      exportResolution: job.export_resolution,
    }),
    []
  )

  // Fetch paginated jobs
  const fetchJobs = useCallback(async () => {
    if (!session?.user?.id) return

    try {
      // Use API route instead of direct Supabase call
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      })

      const apiUrl = `/api/render-jobs/paginated?${params.toString()}`

      const response = await fetch(apiUrl)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ API error:', errorData)
        setJobs([])
        setTotal(0)
        return
      }

      const { data, total } = await response.json()

      // Set total count
      setTotal(total || 0)

      // Normalize and set jobs (same as before)
      const normalized = ((data as RenderJobRow[]) || []).map(normalizeJob)
      setJobs(normalized)
    } catch (error) {
      console.error('Error fetching paginated jobs:', error)
      setJobs([])
      setTotal(0)
    } finally {
      setIsLoading(false)
    }
  }, [session?.user?.id, page, limit, normalizeJob])

  // Initial data fetch and real-time subscription
  useEffect(() => {
    if (!session?.user?.id) return

    setIsLoading(true)
    fetchJobs()

    return () => {
      console.log('🧹 Cleaning up paginated render jobs effect')
    }
  }, [
    session?.user?.id,
    session?.session?.activeOrganizationId,
    page,
    limit,
    fetchJobs,
  ])

  // Separate effect to manage polling based on job status changes
  useEffect(() => {
    if (!session?.user?.id) return

    // Check if there are any active jobs
    const hasActiveJobs = jobs.some(job =>
      ['initializing', 'rendering', 'exporting'].includes(job.status)
    )

    if (hasActiveJobs) {
      const pollInterval = setInterval(() => {
        fetchJobs() // Force fresh fetch
      }, 3000) // Poll every 3 seconds

      return () => {
        clearInterval(pollInterval)
      }
    }
  }, [jobs, session?.user?.id, fetchJobs])

  const refreshJobs = useCallback(() => {
    setIsLoading(true)
    fetchJobs()
  }, [fetchJobs])

  return {
    jobs,
    total,
    isLoading,
    refreshJobs,
  }
}
