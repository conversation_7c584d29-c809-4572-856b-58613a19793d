'use client'

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerClose,
} from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loader2, Youtube, X, Upload, List, ExternalLink } from 'lucide-react'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { YouTubeAITextAssistant } from '@/components/youtube-ai-text-assistant'
import { useStableMediaQuery } from '@/hooks/use-stable-media-query'
import {
  MediaPickerModal,
  Media,
} from '@/app/(scene-editor)/_components/media-picker-modal'
import {
  useYouTubeStatus,
  useYouTubePlaylists,
  handleYouTubeConnectionRevoked,
} from '@/hooks/useYouTubeStatus'

interface YouTubeConnection {
  id: string
  channelInfo: {
    id: string
    title: string
    thumbnailUrl: string | null
    description: string | null
    isVerified?: boolean
    canUploadCustomThumbnails?: boolean
    subscriberCount?: string
    videoCount?: string
    longUploadsStatus?: string
    customUrl?: string | null
  }
  connectedAt: string
  scopes: string[]
  isExpired?: boolean
  expiresAt?: string
}

// Helper function to determine if a channel is fully verified
function isChannelFullyVerified(connection: YouTubeConnection): boolean {
  return connection.channelInfo.longUploadsStatus === 'allowed'
}

// Helper function to get verification status display
function getVerificationStatus(connection: YouTubeConnection) {
  const isFullyVerified = isChannelFullyVerified(connection)
  return {
    isFullyVerified,
    label: isFullyVerified ? 'Verified' : 'Unverified',
    variant: (isFullyVerified ? 'default' : 'secondary') as
      | 'default'
      | 'secondary'
      | 'destructive'
      | 'outline',
  }
}

// Helper function to get capability badges
function getCapabilityBadges(connection: YouTubeConnection) {
  const canUploadThumbnails =
    connection.channelInfo.longUploadsStatus === 'allowed'
  const canUploadLongVideos =
    connection.channelInfo.longUploadsStatus === 'allowed'

  return {
    thumbnail: {
      enabled: canUploadThumbnails,
      label: `Thumbnail uploads: ${canUploadThumbnails ? 'Enabled' : 'Disabled'}`,
      variant: (canUploadThumbnails ? 'default' : 'secondary') as
        | 'default'
        | 'secondary'
        | 'destructive'
        | 'outline',
    },
    longUploads: {
      enabled: canUploadLongVideos,
      label: `Long uploads: ${canUploadLongVideos ? 'Enabled' : 'Disabled'}`,
      variant: (canUploadLongVideos ? 'default' : 'secondary') as
        | 'default'
        | 'secondary'
        | 'destructive'
        | 'outline',
      tooltip: canUploadLongVideos ? undefined : 'Videos limited to 15 minutes',
    },
  }
}

interface YouTubePublishModalProps {
  isOpen: boolean
  onClose: () => void
  videoUrl: string
  videoThumbnail: string | null
  videoTitle?: string
  renderJobId: string
  onPublishSuccess?: (youtubeId: string) => void
}

interface PublishFormData {
  title: string
  description: string
  privacy: 'public' | 'unlisted' | 'private'
  category: string
  tags: string[]
  madeForKids: boolean
  selectedConnection: string
  selectedPlaylist: string
  selectedThumbnail: Media | null
}

const YOUTUBE_CATEGORIES = [
  { value: '1', label: 'Film & Animation' },
  { value: '2', label: 'Autos & Vehicles' },
  { value: '10', label: 'Music' },
  { value: '15', label: 'Pets & Animals' },
  { value: '17', label: 'Sports' },
  { value: '19', label: 'Travel & Events' },
  { value: '20', label: 'Gaming' },
  { value: '22', label: 'People & Blogs' },
  { value: '23', label: 'Comedy' },
  { value: '24', label: 'Entertainment' },
  { value: '25', label: 'News & Politics' },
  { value: '26', label: 'Howto & Style' },
  { value: '27', label: 'Education' },
  { value: '28', label: 'Science & Technology' },
]

// Form content component with simplified props
interface FormContentProps {
  formData: PublishFormData
  currentTag: string
  connections: YouTubeConnection[]
  playlistsData:
    | {
        playlists: Array<{
          id: string
          title: string
          description: string
          thumbnailUrl: string | null
          itemCount: number
        }>
        channelId: string
        channelTitle: string
      }
    | undefined
  playlistsLoading: boolean
  isDesktop: boolean
  videoThumbnail: string | null
  selectedConnection: YouTubeConnection | undefined
  setFormData: React.Dispatch<React.SetStateAction<PublishFormData>>
  setCurrentTag: React.Dispatch<React.SetStateAction<string>>
  setIsMediaPickerOpen: React.Dispatch<React.SetStateAction<boolean>>
  // New props for YouTube connection
  isConnecting: boolean
  onConnect: () => void
  onGoToSettings: () => void
}

const FormContent = React.memo<FormContentProps>(
  ({
    formData,
    currentTag,
    connections,
    playlistsData,
    playlistsLoading,
    isDesktop,
    videoThumbnail,
    selectedConnection,
    setFormData,
    setCurrentTag,
    setIsMediaPickerOpen,
    isConnecting,
    onConnect,
    onGoToSettings,
  }) => {
    // Local handlers for form updates
    const handleTitleChange = (text: string) => {
      setFormData(prev => ({ ...prev, title: text }))
    }

    const handleDescriptionChange = (text: string) => {
      setFormData(prev => ({ ...prev, description: text }))
    }

    const handleTagsChange = (text: string) => {
      const tags = text
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0)
      setFormData(prev => ({ ...prev, tags }))
    }

    const handleAddTag = () => {
      if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
        setFormData(prev => ({
          ...prev,
          tags: [...prev.tags, currentTag.trim()],
        }))
        setCurrentTag('')
      }
    }

    const handleRemoveTag = (tagToRemove: string) => {
      setFormData(prev => ({
        ...prev,
        tags: prev.tags.filter(tag => tag !== tagToRemove),
      }))
    }

    const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleAddTag()
      }
    }

    const handleThumbnailClick = () => {
      setIsMediaPickerOpen(true)
    }

    return (
      <div className={`space-y-5 ${isDesktop ? 'grid grid-cols-2 gap-6' : ''}`}>
        {/* Left Column - Form Fields */}
        <div className='space-y-5'>
          {/* Connection and Playlist Selection - Side by Side */}
          <div className='flex flex-wrap gap-4'>
            <div className='space-y-2'>
              <Label className='text-sm font-medium'>YouTube Channel</Label>
              {connections && connections.length > 0 ? (
                <Select
                  value={formData.selectedConnection}
                  onValueChange={value =>
                    setFormData(prev => ({
                      ...prev,
                      selectedConnection: value,
                    }))
                  }
                >
                  <SelectTrigger className='h-10 w-full'>
                    <SelectValue placeholder='Select channel' />
                  </SelectTrigger>
                  <SelectContent>
                    {connections.map(connection => (
                      <SelectItem key={connection.id} value={connection.id}>
                        <div className='flex items-center gap-2'>
                          <Avatar className='h-5 w-5'>
                            <AvatarImage
                              src={
                                connection.channelInfo.thumbnailUrl || undefined
                              }
                            />
                            <AvatarFallback>
                              <Youtube className='h-3 w-3' />
                            </AvatarFallback>
                          </Avatar>
                          <span className='truncate'>
                            {connection.channelInfo.title}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <div className='space-y-2'>
                  <Button
                    onClick={onConnect}
                    disabled={isConnecting}
                    variant='outline'
                    className='w-full h-10 flex items-center gap-2 border-dashed border-2 hover:border-primary/50 hover:bg-primary/5'
                  >
                    {isConnecting ? (
                      <>
                        <Loader2 className='h-4 w-4 animate-spin' />
                        <span className='text-sm'>Connecting...</span>
                      </>
                    ) : (
                      <>
                        <Youtube className='h-4 w-4 text-red-600' />
                        <span className='text-sm font-medium'>
                          Connect to YouTube
                        </span>
                      </>
                    )}
                  </Button>
                  <p className='text-xs text-muted-foreground text-center'>
                    To connect more channels,{' '}
                    <button
                      onClick={onGoToSettings}
                      className='text-primary hover:text-primary/80 underline underline-offset-2 inline-flex items-center gap-1'
                    >
                      go to Settings
                      <ExternalLink className='h-3 w-3' />
                    </button>
                  </p>
                </div>
              )}
            </div>

            {/* Playlist Selection */}
            <div className='space-y-2'>
              <Label className='text-sm font-medium'>Playlist (Optional)</Label>
              {formData.selectedConnection ? (
                playlistsLoading ? (
                  <div className='flex items-center gap-2 p-2 border rounded-lg h-10'>
                    <Loader2 className='h-4 w-4 animate-spin' />
                    <span className='text-xs text-muted-foreground'>
                      Loading...
                    </span>
                  </div>
                ) : (
                  <Select
                    value={formData.selectedPlaylist}
                    onValueChange={value =>
                      setFormData(prev => ({
                        ...prev,
                        selectedPlaylist: value,
                      }))
                    }
                  >
                    <SelectTrigger className='h-10 w-full'>
                      <SelectValue placeholder='No playlist' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='none'>
                        <div className='flex items-center gap-2'>
                          <Youtube className='h-3 w-3' />
                          <span>No playlist</span>
                        </div>
                      </SelectItem>
                      {playlistsData?.playlists.map(playlist => (
                        <SelectItem key={playlist.id} value={playlist.id}>
                          <div className='flex items-center gap-2'>
                            <List className='h-3 w-3' />
                            <span className='truncate'>{playlist.title}</span>
                            <Badge
                              variant='secondary'
                              className='ml-auto text-xs'
                            >
                              {playlist.itemCount}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )
              ) : (
                <div className='h-10 border rounded-lg bg-muted/30 flex items-center px-3'>
                  <span className='text-xs text-muted-foreground'>
                    Select channel first
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Title */}
          <div className='space-y-2'>
            <Label htmlFor='title' className='text-sm font-medium'>
              Title
            </Label>
            <div className='relative'>
              <Input
                id='title'
                value={formData.title}
                onChange={e =>
                  setFormData(prev => ({ ...prev, title: e.target.value }))
                }
                placeholder='Enter video title...'
                maxLength={100}
                className='h-10 pr-20'
              />
              <div className='absolute right-2 top-2 flex items-center gap-1'>
                <YouTubeAITextAssistant
                  text={formData.title}
                  onTextChange={handleTitleChange}
                  placeholder=''
                  buttonSize='sm'
                  buttonClassName='h-6 w-6 p-0 rounded hover:bg-muted text-muted-foreground hover:text-foreground border-0'
                  fieldType='title'
                  formData={formData}
                />
                <span className='text-xs text-muted-foreground'>
                  {formData.title.length}/100
                </span>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className='space-y-2'>
            <Label htmlFor='description' className='text-sm font-medium'>
              Description
            </Label>
            <div className='relative'>
              <Textarea
                id='description'
                value={formData.description}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder='Enter video description...'
                rows={3}
                maxLength={5000}
                className='resize-none pr-16'
              />
              <div className='absolute right-2 bottom-2 flex items-center gap-1'>
                <YouTubeAITextAssistant
                  text={formData.description}
                  onTextChange={handleDescriptionChange}
                  placeholder=''
                  buttonSize='sm'
                  buttonClassName='h-6 w-6 p-0 rounded hover:bg-muted text-foreground hover:text-foreground border-0'
                  fieldType='description'
                  formData={formData}
                />
                <span className='text-xs text-muted-foreground'>
                  {formData.description.length}/5000
                </span>
              </div>
            </div>
          </div>

          {/* Privacy & Category - Side by Side */}
          <div className='flex flex-wrap gap-4'>
            <div className='space-y-2'>
              <Label className='text-sm font-medium'>Privacy</Label>
              <Select
                value={formData.privacy}
                onValueChange={(value: 'public' | 'unlisted' | 'private') =>
                  setFormData(prev => ({ ...prev, privacy: value }))
                }
              >
                <SelectTrigger className='h-10 w-full'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='public'>Public</SelectItem>
                  <SelectItem value='unlisted'>Unlisted</SelectItem>
                  <SelectItem value='private'>Private</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <Label className='text-sm font-medium'>Category</Label>
              <Select
                value={formData.category}
                onValueChange={value =>
                  setFormData(prev => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger className='h-10 w-full'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {YOUTUBE_CATEGORIES.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Made for Kids - Toggle Switch */}
          <div className='space-y-2'>
            <Label className='text-sm font-medium'>Audience</Label>
            <div className='flex items-center justify-between p-3 border rounded-lg bg-muted/30'>
              <div className='space-y-0.5'>
                <p className='text-sm font-medium'>Made for kids</p>
                <p className='text-xs text-muted-foreground'>
                  {formData.madeForKids
                    ? 'This video is designed for children under 13'
                    : 'This video is not specifically designed for children'}
                </p>
              </div>
              <Switch
                checked={formData.madeForKids}
                onCheckedChange={checked =>
                  setFormData(prev => ({ ...prev, madeForKids: checked }))
                }
              />
            </div>
          </div>

          {/* Tags */}
          <div className='space-y-2'>
            <Label className='text-sm font-medium'>Tags</Label>
            <div className='space-y-2'>
              <div className='relative'>
                <Input
                  value={currentTag}
                  onChange={e => setCurrentTag(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder='Add tags...'
                  className='h-10 pr-20'
                />
                <div className='absolute right-2 top-2 flex items-center gap-1'>
                  <YouTubeAITextAssistant
                    text={formData.tags.join(', ')}
                    onTextChange={handleTagsChange}
                    placeholder=''
                    buttonSize='sm'
                    buttonClassName='h-6 w-6 p-0 rounded hover:bg-muted text-muted-foreground hover:text-foreground border-0'
                    fieldType='tags'
                    formData={formData}
                  />
                  <Button
                    type='button'
                    onClick={handleAddTag}
                    size='sm'
                    className='h-6 px-2 text-xs'
                  >
                    Add
                  </Button>
                </div>
              </div>
              {formData.tags.length > 0 && (
                <div className='flex flex-wrap gap-1.5'>
                  {formData.tags.map(tag => (
                    <Badge
                      key={tag}
                      variant='secondary'
                      className='text-xs py-0.5 px-2'
                    >
                      {tag}
                      <button
                        type='button'
                        onClick={() => handleRemoveTag(tag)}
                        className='ml-1 hover:text-destructive transition-colors'
                      >
                        <X className='h-3 w-3' />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Preview (Desktop only) */}
        {isDesktop && (
          <div className='space-y-6'>
            {/* Video Preview */}
            <div className='space-y-3'>
              <Label className='text-sm font-medium'>Video Preview</Label>
              <div
                className='aspect-video bg-black rounded-lg overflow-hidden border cursor-pointer hover:border-primary transition-colors'
                onClick={handleThumbnailClick}
                title='Click to select custom thumbnail'
              >
                {formData.selectedThumbnail ? (
                  <img
                    src={formData.selectedThumbnail.thumbnail}
                    alt='Selected thumbnail'
                    className='w-full h-full object-cover'
                  />
                ) : videoThumbnail ? (
                  <img
                    src={videoThumbnail}
                    alt='Video thumbnail'
                    className='w-full h-full object-cover'
                  />
                ) : (
                  <div className='w-full h-full flex items-center justify-center text-muted-foreground hover:text-primary transition-colors'>
                    <div className='text-center'>
                      <Upload className='h-8 w-8 mx-auto mb-2' />
                      <p className='text-sm'>Click to select thumbnail</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Selected Channel Info */}
            {selectedConnection && (
              <div className='p-4 border rounded-lg bg-muted/30'>
                <Label className='text-sm font-medium mb-3 block'>
                  Publishing to
                </Label>
                <div className='space-y-3'>
                  <div className='flex items-center gap-3'>
                    <Avatar className='h-10 w-10'>
                      <AvatarImage
                        src={
                          selectedConnection.channelInfo.thumbnailUrl ||
                          undefined
                        }
                      />
                      <AvatarFallback>
                        <Youtube className='h-5 w-5' />
                      </AvatarFallback>
                    </Avatar>
                    <div className='flex-1'>
                      <div className='flex items-center gap-2'>
                        <p className='font-medium text-sm'>
                          {selectedConnection.channelInfo.title}
                        </p>
                        <Badge
                          variant={
                            getVerificationStatus(selectedConnection).variant
                          }
                          className='text-xs'
                        >
                          {getVerificationStatus(selectedConnection).label}
                        </Badge>
                      </div>
                      <p className='text-xs text-muted-foreground'>
                        YouTube Channel
                      </p>
                    </div>
                  </div>

                  {/* Capability Badges */}
                  <div className='flex flex-wrap gap-2'>
                    <Badge
                      variant={
                        getCapabilityBadges(selectedConnection).thumbnail
                          .variant
                      }
                      className='text-xs'
                    >
                      {getCapabilityBadges(selectedConnection).thumbnail.label}
                    </Badge>
                    <Badge
                      variant={
                        getCapabilityBadges(selectedConnection).longUploads
                          .variant
                      }
                      className='text-xs'
                      title={
                        getCapabilityBadges(selectedConnection).longUploads
                          .tooltip
                      }
                    >
                      {
                        getCapabilityBadges(selectedConnection).longUploads
                          .label
                      }
                    </Badge>
                  </div>
                </div>
              </div>
            )}

            {/* Publish Preview Card */}
            <div className='p-4 border rounded-lg bg-card'>
              <Label className='text-sm font-medium mb-3 block'>
                Publish Settings
              </Label>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span className='text-muted-foreground'>Privacy:</span>
                  <span className='capitalize font-medium'>
                    {formData.privacy}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-muted-foreground'>Category:</span>
                  <span className='font-medium'>
                    {
                      YOUTUBE_CATEGORIES.find(
                        c => c.value === formData.category
                      )?.label
                    }
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-muted-foreground'>Tags:</span>
                  <span className='font-medium'>
                    {formData.tags.length} tags
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-muted-foreground'>Made for kids:</span>
                  <span className='font-medium'>
                    {formData.madeForKids ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  },
  // Custom comparison function to optimize re-renders
  (prevProps: FormContentProps, nextProps: FormContentProps) => {
    // Compare formData deeply since it's the most likely to change
    const formDataEqual =
      prevProps.formData.title === nextProps.formData.title &&
      prevProps.formData.description === nextProps.formData.description &&
      prevProps.formData.privacy === nextProps.formData.privacy &&
      prevProps.formData.category === nextProps.formData.category &&
      prevProps.formData.madeForKids === nextProps.formData.madeForKids &&
      prevProps.formData.selectedConnection ===
        nextProps.formData.selectedConnection &&
      prevProps.formData.selectedPlaylist ===
        nextProps.formData.selectedPlaylist &&
      prevProps.formData.tags.length === nextProps.formData.tags.length &&
      prevProps.formData.tags.every(
        (tag: string, index: number) => tag === nextProps.formData.tags[index]
      ) &&
      prevProps.formData.selectedThumbnail?.id ===
        nextProps.formData.selectedThumbnail?.id

    // Compare other props
    return (
      formDataEqual &&
      prevProps.currentTag === nextProps.currentTag &&
      prevProps.playlistsLoading === nextProps.playlistsLoading &&
      prevProps.isDesktop === nextProps.isDesktop &&
      prevProps.videoThumbnail === nextProps.videoThumbnail &&
      prevProps.selectedConnection?.id === nextProps.selectedConnection?.id &&
      prevProps.connections?.length === nextProps.connections?.length &&
      prevProps.playlistsData?.playlists?.length ===
        nextProps.playlistsData?.playlists?.length &&
      prevProps.isConnecting === nextProps.isConnecting
    )
  }
)

FormContent.displayName = 'FormContent'

interface FooterProps {
  onClose: () => void
  onPublish: () => void
  isPublishing: boolean
  formData: PublishFormData
}

const Footer = React.memo<FooterProps>(
  ({ onClose, onPublish, isPublishing, formData }) => (
    <div className='flex flex-col-reverse sm:flex-row justify-end gap-3'>
      <Button
        variant='outline'
        onClick={onClose}
        disabled={isPublishing}
        className='h-10'
      >
        Cancel
      </Button>
      <Button
        onClick={onPublish}
        disabled={
          isPublishing || !formData.selectedConnection || !formData.title.trim()
        }
        className='h-10'
      >
        {isPublishing ? (
          <>
            <Loader2 className='h-4 w-4 mr-2 animate-spin' />
            Publishing...
          </>
        ) : (
          <>
            <Upload className='h-4 w-4 mr-2' />
            Publish to YouTube
          </>
        )}
      </Button>
    </div>
  )
)

Footer.displayName = 'Footer'

export function YouTubePublishModal({
  isOpen,
  onClose,
  videoUrl: _videoUrl, // eslint-disable-line @typescript-eslint/no-unused-vars
  videoThumbnail,
  videoTitle = '',
  renderJobId,
  onPublishSuccess,
}: YouTubePublishModalProps) {
  const [isPublishing, setIsPublishing] = useState(false)
  const [currentTag, setCurrentTag] = useState('')
  const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [formData, setFormData] = useState<PublishFormData>({
    title: videoTitle,
    description: '',
    privacy: 'public',
    category: '24', // Entertainment
    tags: [],
    madeForKids: false,
    selectedConnection: '',
    selectedPlaylist: 'none',
    selectedThumbnail: null,
  })

  const queryClient = useQueryClient()
  const router = useRouter()
  const isDesktop = useStableMediaQuery('(min-width: 768px)')

  // Use the centralized YouTube status hook
  const { connections } = useYouTubeStatus()

  // Debug log to see what connections we have

  // Fetch playlists for selected connection using the centralized hook
  const {
    data: playlistsData,
    isLoading: playlistsLoading,
    error: playlistsError,
  } = useYouTubePlaylists(
    formData.selectedConnection,
    !!formData.selectedConnection && isOpen
  )

  // Handle playlist errors by clearing the selected connection
  if (playlistsError && playlistsError.message === 'CONNECTION_REVOKED') {
    setFormData(prev => ({
      ...prev,
      selectedConnection: '',
      selectedPlaylist: 'none',
    }))
  }

  // Auto-select first connection if only one exists
  useEffect(() => {
    if (
      connections &&
      connections.length === 1 &&
      !formData.selectedConnection
    ) {
      setFormData(prev => ({ ...prev, selectedConnection: connections[0].id }))
    }
  }, [connections, formData.selectedConnection])

  // Reset playlist selection when connection changes
  const selectedConnectionId = formData.selectedConnection
  useEffect(() => {
    setFormData(prev => ({ ...prev, selectedPlaylist: 'none' }))
  }, [selectedConnectionId])

  // Memoized event handlers to prevent unnecessary re-renders
  // Simplified thumbnail handlers
  const handleThumbnailSelect = useCallback((media: Media) => {
    setFormData(prev => ({ ...prev, selectedThumbnail: media }))
    setIsMediaPickerOpen(false)
  }, [])

  const handleThumbnailClick = useCallback(() => {
    setIsMediaPickerOpen(true)
  }, [])

  // Handle YouTube connection (reused from settings)
  const handleConnect = useCallback(async () => {
    setIsConnecting(true)

    // Open popup immediately with static loading page to prevent blocking
    const popup = window.open(
      '/auth/youtube/loading.html',
      'youtube-auth',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    )

    if (!popup) {
      setIsConnecting(false)
      toast.error('Popup blocked. Please allow popups for this site.')
      return
    }

    try {
      // Listen for messages from popup
      const handleMessage = (event: MessageEvent) => {
        // Allow messages from the same origin
        if (event.origin !== window.location.origin) {
          return
        }

        if (event.data.type === 'YOUTUBE_AUTH_SUCCESS') {
          toast.success('YouTube account connected successfully!')
          // Invalidate the correct query key used by useYouTubeStatus hook
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
          // Also invalidate playlists to fetch them immediately for the new connection
          queryClient.invalidateQueries({
            queryKey: ['youtube-playlists'],
          })
          setIsConnecting(false)
          window.removeEventListener('message', handleMessage)
          popup.close()
        } else if (event.data.type === 'YOUTUBE_AUTH_ERROR') {
          toast.error(`Connection failed: ${event.data.error}`)
          // Also refresh status on error to ensure UI is up-to-date
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
          setIsConnecting(false)
          window.removeEventListener('message', handleMessage)
          popup.close()
        } else if (event.data.type === 'YOUTUBE_LOADING_READY') {
          // Now make the API call to get the OAuth URL
          initiateOAuth(popup)
        }
      }

      window.addEventListener('message', handleMessage)

      // Check if popup was closed manually
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed)
          setIsConnecting(false)
          window.removeEventListener('message', handleMessage)
          // Refresh YouTube status when popup closes to ensure UI is up-to-date
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
        }
      }, 1000)

      // Function to initiate OAuth after loading page is ready
      const initiateOAuth = async (popupWindow: Window) => {
        try {
          // Get authorization URL
          const response = await fetch('/api/youtube/auth/initiate')
          if (!response.ok) {
            throw new Error('Failed to initiate YouTube connection')
          }

          const { authUrl } = await response.json()

          // Navigate popup to OAuth URL
          popupWindow.postMessage(
            {
              type: 'NAVIGATE_TO_OAUTH',
              url: authUrl,
            },
            window.location.origin
          )
        } catch (error) {
          console.error('OAuth initiation error:', error)
          toast.error('Failed to initiate YouTube connection')
          setIsConnecting(false)
          popupWindow.close()
          window.removeEventListener('message', handleMessage)
          // Refresh status on OAuth initiation error
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
        }
      }

      // Timeout after 5 minutes
      setTimeout(
        () => {
          if (!popup.closed) {
            popup.close()
            setIsConnecting(false)
            window.removeEventListener('message', handleMessage)
            // Refresh status on timeout to ensure UI is up-to-date
            queryClient.invalidateQueries({
              queryKey: ['youtube-connections'],
            })
            toast.error('Connection timeout. Please try again.')
          }
        },
        5 * 60 * 1000
      )
    } catch (error) {
      console.error('Connection error:', error)
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to connect YouTube account'
      )
      setIsConnecting(false)
    }
  }, [queryClient])

  // Handle navigation to settings
  const handleGoToSettings = useCallback(() => {
    router.push('/settings')
    onClose()
  }, [router, onClose])

  const handlePublish = async () => {
    if (!formData.selectedConnection) {
      toast.error('Please select a YouTube channel')
      return
    }

    if (!formData.title.trim()) {
      toast.error('Please enter a video title')
      return
    }

    setIsPublishing(true)

    try {
      const response = await fetch('/api/youtube/publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          renderJobId,
          connectionId: formData.selectedConnection,
          metadata: {
            title: formData.title,
            description: formData.description,
            privacy: formData.privacy,
            categoryId: formData.category,
            tags: formData.tags,
            madeForKids: formData.madeForKids,
            playlistId:
              formData.selectedPlaylist === 'none'
                ? undefined
                : formData.selectedPlaylist,
            thumbnailUrl:
              formData.selectedThumbnail?.url ||
              formData.selectedThumbnail?.src,
          },
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()

        // Handle specific CONNECTION_REVOKED error
        if (
          response.status === 410 &&
          errorData.code === 'CONNECTION_REVOKED'
        ) {
          // Automatically disconnect the invalid connection
          await handleYouTubeConnectionRevoked(
            queryClient,
            formData.selectedConnection,
            true
          )

          // Clear the selected connection since it's no longer valid
          setFormData(prev => ({
            ...prev,
            selectedConnection: '',
            selectedPlaylist: 'none',
          }))

          throw new Error(
            'YouTube connection was revoked. Please reconnect your account and try again.'
          )
        }

        throw new Error(errorData.message || 'Failed to publish video')
      }

      const { youtubeId } = await response.json()

      // Invalidate render jobs cache to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['render-jobs'] })
      queryClient.invalidateQueries({ queryKey: ['paginated-render-jobs'] })

      toast.success('Video published to YouTube successfully!')
      onPublishSuccess?.(youtubeId)
      onClose()
    } catch (error) {
      console.error('Publish error:', error)
      toast.error(
        error instanceof Error ? error.message : 'Failed to publish video'
      )
    } finally {
      setIsPublishing(false)
    }
  }

  // Memoize selectedConnection to prevent unnecessary re-renders
  const selectedConnection = useMemo(
    () => connections?.find(conn => conn.id === formData.selectedConnection),
    [connections, formData.selectedConnection]
  )

  // Desktop Modal
  const renderDesktopModal = () => (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='w-[95%] min-w-[300px] sm:min-w-[600px] md:min-w-[900px] max-h-[90vh] p-0 flex flex-col overflow-hidden'>
        <DialogHeader className='px-6 pt-6 pb-4 border-b border-border/50'>
          <DialogTitle className='flex items-center gap-2 text-xl font-semibold'>
            <Youtube className='h-5 w-5 text-red-600' />
            Publish to YouTube
          </DialogTitle>
        </DialogHeader>

        <div className='flex-1 overflow-y-auto px-6 py-6 scrollbar-thin'>
          <FormContent
            formData={formData}
            currentTag={currentTag}
            connections={connections}
            playlistsData={playlistsData}
            playlistsLoading={playlistsLoading}
            isDesktop={isDesktop}
            videoThumbnail={videoThumbnail}
            selectedConnection={selectedConnection}
            setFormData={setFormData}
            setCurrentTag={setCurrentTag}
            setIsMediaPickerOpen={setIsMediaPickerOpen}
            isConnecting={isConnecting}
            onConnect={handleConnect}
            onGoToSettings={handleGoToSettings}
          />
        </div>

        <div className='px-6 py-4 border-t border-border/50 bg-muted/20'>
          <Footer
            onClose={onClose}
            onPublish={handlePublish}
            isPublishing={isPublishing}
            formData={formData}
          />
        </div>
      </DialogContent>
    </Dialog>
  )

  // Mobile Drawer
  const renderMobileModal = () => (
    <Drawer open={isOpen} onOpenChange={onClose} shouldScaleBackground>
      <DrawerContent className='h-[85vh] max-h-[100vh] overflow-hidden flex flex-col'>
        <DrawerHeader className='px-6 pt-6 pb-4 border-b border-border/50'>
          <DrawerTitle className='flex items-center gap-2 text-xl font-semibold'>
            <Youtube className='h-5 w-5 text-red-600' />
            Publish to YouTube
          </DrawerTitle>
          <DrawerClose asChild>
            <Button
              variant='outline'
              size='icon'
              className='absolute right-4 top-4'
              onClick={onClose}
            >
              <X className='h-4 w-4' />
              <span className='sr-only'>Close</span>
            </Button>
          </DrawerClose>
        </DrawerHeader>

        <div className='flex-1 overflow-y-auto px-6 py-6 scrollbar-thin'>
          <FormContent
            formData={formData}
            currentTag={currentTag}
            connections={connections}
            playlistsData={playlistsData}
            playlistsLoading={playlistsLoading}
            isDesktop={isDesktop}
            videoThumbnail={videoThumbnail}
            selectedConnection={selectedConnection}
            setFormData={setFormData}
            setCurrentTag={setCurrentTag}
            setIsMediaPickerOpen={setIsMediaPickerOpen}
            isConnecting={isConnecting}
            onConnect={handleConnect}
            onGoToSettings={handleGoToSettings}
          />

          {/* Mobile Video Preview */}
          <div className='mt-6 space-y-3'>
            <Label className='text-sm font-medium'>Video Preview</Label>
            <div
              className='aspect-video bg-black rounded-lg overflow-hidden border cursor-pointer hover:border-primary transition-colors'
              onClick={handleThumbnailClick}
              title='Click to select custom thumbnail'
            >
              {formData.selectedThumbnail ? (
                <img
                  src={formData.selectedThumbnail.thumbnail}
                  alt='Selected thumbnail'
                  className='w-full h-full object-cover'
                />
              ) : videoThumbnail ? (
                <img
                  src={videoThumbnail}
                  alt='Video thumbnail'
                  className='w-full h-full object-cover'
                />
              ) : (
                <div className='w-full h-full flex items-center justify-center text-muted-foreground hover:text-primary transition-colors'>
                  <div className='text-center'>
                    <Upload className='h-8 w-8 mx-auto mb-2' />
                    <p className='text-sm'>Click to select thumbnail</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Mobile Channel Info */}
          {selectedConnection && (
            <div className='mt-4 p-4 border rounded-lg bg-muted/30'>
              <Label className='text-sm font-medium mb-3 block'>
                Publishing to
              </Label>
              <div className='space-y-3'>
                <div className='flex items-center gap-3'>
                  <Avatar className='h-10 w-10'>
                    <AvatarImage
                      src={
                        selectedConnection.channelInfo.thumbnailUrl || undefined
                      }
                    />
                    <AvatarFallback>
                      <Youtube className='h-5 w-5' />
                    </AvatarFallback>
                  </Avatar>
                  <div className='flex-1'>
                    <div className='flex items-center gap-2'>
                      <p className='font-medium text-sm'>
                        {selectedConnection.channelInfo.title}
                      </p>
                      <Badge
                        variant={
                          getVerificationStatus(selectedConnection).variant
                        }
                        className='text-xs'
                      >
                        {getVerificationStatus(selectedConnection).label}
                      </Badge>
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      YouTube Channel
                    </p>
                  </div>
                </div>

                {/* Capability Badges */}
                <div className='flex flex-wrap gap-2'>
                  <Badge
                    variant={
                      getCapabilityBadges(selectedConnection).thumbnail.variant
                    }
                    className='text-xs'
                  >
                    {getCapabilityBadges(selectedConnection).thumbnail.label}
                  </Badge>
                  <Badge
                    variant={
                      getCapabilityBadges(selectedConnection).longUploads
                        .variant
                    }
                    className='text-xs'
                    title={
                      getCapabilityBadges(selectedConnection).longUploads
                        .tooltip
                    }
                  >
                    {getCapabilityBadges(selectedConnection).longUploads.label}
                  </Badge>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className='px-6 py-4 border-t border-border/50 bg-muted/20'>
          <Footer
            onClose={onClose}
            onPublish={handlePublish}
            isPublishing={isPublishing}
            formData={formData}
          />
        </div>
      </DrawerContent>
    </Drawer>
  )

  return (
    <>
      {isDesktop ? renderDesktopModal() : renderMobileModal()}

      {/* Media Picker Modal for Thumbnail Selection */}
      <MediaPickerModal
        open={isMediaPickerOpen}
        onClose={() => setIsMediaPickerOpen(false)}
        onSelect={handleThumbnailSelect}
        filterMediaTypes={['image']} // Only show images for thumbnail selection
      />
    </>
  )
}
