/**
 * Remotion Root Component - Refactored
 *
 * This is the new, clean Root.tsx that uses our shared Remotion library
 * instead of the massive inline implementation. It maintains identical
 * functionality while being much more maintainable.
 */

'use client'
import React from 'react'
import { Composition } from 'remotion'
import { CaptionStyle, UnifiedComposition, Scene } from '../src/lib/remotion'

// Define the props interface for the composition
interface CompositionProps {
  scenes: Scene[]
  subtitlePosition?: { x: number; y: number }
  selectedMusic?: {
    id: string
    title: string
    genre: string
    mood: string
    artistName: string
    artistUrl?: string
    provider: string
    licenseId: string
    sourceUrl?: string
    previewUrl: string
    durationMillis: number
  } | null
  musicVolume?: number
  musicEnabled?: boolean
  compositionWidth?: number
  compositionHeight?: number
  speech?: {
    enabled: boolean
    src: string
    name: string
    volume: number
    transcript: {
      captions: Array<{
        start: number
        end: number
        sentence: string
        wordBoundries: Array<{ start: number; end: number; word: string }>
      }>
      status: string
    }
  } | null
  captionStyle?: CaptionStyle
}

// Wrapper component to handle props properly
const RemotionComposition: React.FC<Record<string, unknown>> = props => {
  return (
    <UnifiedComposition
      {...(props as unknown as CompositionProps)}
      isRenderingContext={true}
    />
  )
}

export const Root: React.FC = () => {
  return (
    <Composition
      id='main'
      component={RemotionComposition}
      durationInFrames={300} // This will be overridden by calculateMetadata
      fps={30}
      width={960} // This will be overridden by calculateMetadata
      height={540} // This will be overridden by calculateMetadata
      defaultProps={{
        scenes: [],
        subtitlePosition: { x: 240, y: 246 },
        selectedMusic: null,
        musicVolume: 50,
        musicEnabled: true,
        compositionWidth: 960,
        compositionHeight: 540,
        speech: null, // Will be passed via inputProps during render for audio/podcast workflows
        captionStyle: undefined, // Will be passed via inputProps during render
      }}
      calculateMetadata={({ props }) => {
        // Cast props to our expected type
        const typedProps = props as unknown as CompositionProps

        // Calculate total duration from scenes or speech
        const validScenes =
          typedProps.scenes?.filter(
            (scene: Scene) =>
              typeof scene.voiceover?.audioDuration === 'number' &&
              scene.voiceover.audioDuration > 0
          ) || []

        const totalDuration =
          typedProps.speech?.transcript?.captions &&
          typedProps.speech.transcript.captions.length > 0
            ? // For speech-based workflows, use speech duration from captions
              Math.max(
                ...typedProps.speech.transcript.captions.map(
                  caption => caption.end
                )
              )
            : // For text-based workflows, use scene voiceover durations
              validScenes.reduce(
                (acc: number, scene: Scene) =>
                  acc + (scene.voiceover?.audioDuration || 0),
                0
              )
        const durationInFrames = Math.round(totalDuration * 30) // 30 fps

        // Get dimensions from props
        const width = typedProps.compositionWidth || 960
        const height = typedProps.compositionHeight || 540

        return {
          durationInFrames,
          width,
          height,
          fps: 30,
        }
      }}
    />
  )
}
